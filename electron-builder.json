{"appId": "com.mustafakashaf.furniture.sales", "productName": "مصطفي كشاف للمفروشات", "directories": {"output": "dist", "buildResources": "assets"}, "files": ["main.js", "src/**/*", "data/**/*", "node_modules/**/*", "package.json"], "win": {"target": [{"target": "nsis", "arch": ["ia32"]}, {"target": "portable", "arch": ["ia32"]}], "icon": "assets/icon.ico", "requestedExecutionLevel": "asInvoker"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "مصطفي كشاف للمفروشات", "installerIcon": "assets/icon.ico", "uninstallerIcon": "assets/icon.ico", "installerHeaderIcon": "assets/icon.ico", "deleteAppDataOnUninstall": false, "displayLanguageSelector": false, "language": "1025"}, "portable": {"artifactName": "مصطفي-كشاف-للمفروشات-${version}-portable.exe"}, "publish": null}