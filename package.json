{"name": "mustafa-kashaf-furniture-sales", "version": "1.0.0", "description": "برنامج مبيعات احترافي لمعرض مصطفي كشاف للمفروشات", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "dist": "electron-builder --win --ia32 --publish=never", "dist-win32": "electron-builder --win --ia32 --publish=never", "pack": "electron-builder --dir", "clean": "<PERSON><PERSON><PERSON> dist", "rebuild": "npm run clean && npm run dist"}, "keywords": ["sales", "furniture", "invoice", "desktop-app"], "author": "Mustafa Ka<PERSON>f Furniture Store", "license": "MIT", "devDependencies": {"electron": "^22.3.27", "electron-builder": "^24.6.4"}, "dependencies": {"uuid": "^9.0.1"}, "build": {"appId": "com.mustafakashaf.furniture.sales", "productName": "مصطفي كشاف للمفروشات", "directories": {"output": "dist", "buildResources": "assets"}, "files": ["main.js", "src/**/*", "data/**/*", "node_modules/**/*", "package.json", "README.md"], "extraFiles": [{"from": "تعليمات_التشغيل.txt", "to": "تعليمات_التشغيل.txt"}, {"from": "دليل_التثبيت_والاستخدام.txt", "to": "دليل_التثبيت_والاستخدام.txt"}, {"from": "start.bat", "to": "start.bat"}], "win": {"target": [{"target": "nsis", "arch": ["ia32"]}, {"target": "portable", "arch": ["ia32"]}], "icon": "assets/icon.ico", "requestedExecutionLevel": "asInvoker"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "allowElevation": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "مصطفي كشاف للمفروشات", "installerIcon": "assets/icon.ico", "uninstallerIcon": "assets/icon.ico", "deleteAppDataOnUninstall": false, "runAfterFinish": true, "menuCategory": "Business", "artifactName": "<PERSON>-<PERSON><PERSON>f-Furniture-Setup-${version}.exe", "include": "installer.nsh"}, "portable": {"artifactName": "مصطفي-كشاف-للمفروشات-Portable-${version}.exe"}, "publish": null}}