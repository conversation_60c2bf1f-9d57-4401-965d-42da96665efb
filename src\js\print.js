// إدارة الطباعة
const { ipcRenderer } = require('electron');

class PrintManager {
    constructor() {
        this.settings = storage.readFile('settings.json') || {};
    }

    // إنشاء قالب الفاتورة للطباعة
    generateInvoiceTemplate(invoice) {
        const settings = this.settings;
        const currentDate = new Date().toLocaleDateString('ar-EG');
        const currentTime = new Date().toLocaleTimeString('ar-EG');

        return `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>فاتورة ${invoice.invoiceNumber} - مصطفي كشاف للمفروشات</title>
            <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;800&display=swap" rel="stylesheet">
            <style>
                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }
                
                body {
                    font-family: 'Cairo', 'Arial', sans-serif;
                    font-size: 13px;
                    line-height: 1.5;
                    color: #2d3748;
                    background: white;
                    padding: 0;
                    margin: 0;
                    width: 210mm;
                    min-height: 297mm;
                }

                /* إعدادات الطباعة لورق A4 */
                @page {
                    size: A4;
                    margin: 15mm;
                }
                
                .invoice-container {
                    width: 100%;
                    max-width: 180mm;
                    margin: 0 auto;
                    background: white;
                    border-radius: 15px;
                    overflow: hidden;
                    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
                    border: 2px solid transparent;
                    background-clip: padding-box;
                    position: relative;
                    min-height: 260mm;
                }

                .invoice-container::before {
                    content: '';
                    position: absolute;
                    top: -2px;
                    left: -2px;
                    right: -2px;
                    bottom: -2px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
                    border-radius: 17px;
                    z-index: -1;
                }
                
                .invoice-header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
                    color: white;
                    padding: 25mm 20mm;
                    text-align: center;
                    position: relative;
                    overflow: hidden;
                    height: 60mm;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .invoice-header::before {
                    content: '';
                    position: absolute;
                    top: -50%;
                    left: -50%;
                    width: 200%;
                    height: 200%;
                    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="pattern" width="30" height="30" patternUnits="userSpaceOnUse"><circle cx="15" cy="15" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="5" cy="5" r="1" fill="rgba(255,255,255,0.05)"/><circle cx="25" cy="25" r="1.5" fill="rgba(255,255,255,0.08)"/></pattern></defs><rect width="100" height="100" fill="url(%23pattern)"/></svg>');
                    opacity: 0.4;
                    animation: float 20s ease-in-out infinite;
                }

                @keyframes float {
                    0%, 100% { transform: translateY(0px) rotate(0deg); }
                    50% { transform: translateY(-10px) rotate(2deg); }
                }
                
                .store-info {
                    position: relative;
                    z-index: 1;
                }
                
                .store-name {
                    font-size: 2.2rem;
                    font-weight: 800;
                    margin-bottom: 8mm;
                    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
                    letter-spacing: 1px;
                    line-height: 1.2;
                }

                .store-details {
                    font-size: 1rem;
                    opacity: 0.95;
                    font-weight: 500;
                    text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
                    line-height: 1.4;
                }

                .header-decoration {
                    position: absolute;
                    top: 8mm;
                    right: 8mm;
                    width: 20mm;
                    height: 20mm;
                    border: 2px solid rgba(255,255,255,0.3);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 1.5rem;
                    background: rgba(255,255,255,0.1);
                }

                .header-decoration-left {
                    left: 8mm;
                    right: auto;
                }

                .company-logo {
                    position: absolute;
                    top: 50%;
                    left: 8mm;
                    transform: translateY(-50%);
                    width: 15mm;
                    height: 15mm;
                    background: rgba(255,255,255,0.2);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 1.2rem;
                    border: 2px solid rgba(255,255,255,0.3);
                }
                
                .invoice-info {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 15mm;
                    padding: 15mm 20mm;
                    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                    position: relative;
                    min-height: 40mm;
                }

                .invoice-info::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 50%;
                    transform: translateX(-50%);
                    width: 2px;
                    height: 100%;
                    background: linear-gradient(to bottom, transparent, #cbd5e0, transparent);
                }
                
                .info-section {
                    background: white;
                    padding: 8mm;
                    border-radius: 10px;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
                    border: 1px solid #e2e8f0;
                    position: relative;
                }

                .info-section::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 3px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 10px 10px 0 0;
                }

                .info-section h3 {
                    color: #2d3748;
                    font-size: 1.4rem;
                    margin-bottom: 20px;
                    font-weight: 700;
                    display: flex;
                    align-items: center;
                    gap: 10px;
                }

                .info-section h3::before {
                    content: '📋';
                    font-size: 1.2rem;
                }

                .customer-details h3::before {
                    content: '👤';
                }
                
                .info-row {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 8px;
                    padding: 5px 0;
                }
                
                .info-row .label {
                    font-weight: 600;
                    color: #666;
                }
                
                .info-row .value {
                    font-weight: 700;
                    color: #333;
                }
                
                .invoice-number {
                    background: #667eea;
                    color: white;
                    padding: 8px 16px;
                    border-radius: 20px;
                    font-weight: bold;
                    display: inline-block;
                }
                
                .items-section {
                    margin: 10mm 0;
                    background: white;
                    border-radius: 12px;
                    overflow: hidden;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
                    page-break-inside: avoid;
                }

                .items-header {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 8mm 10mm;
                    font-size: 1.1rem;
                    font-weight: 700;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                }

                .items-header::before {
                    content: '🛍️';
                    font-size: 1.5rem;
                }

                .items-table {
                    margin: 0;
                    border-collapse: collapse;
                    width: 100%;
                    background: white;
                }

                .items-table th {
                    background: linear-gradient(135deg, #4c51bf 0%, #553c9a 100%);
                    color: white;
                    padding: 6mm 8mm;
                    text-align: right;
                    font-weight: 700;
                    font-size: 0.95rem;
                    border-bottom: 2px solid #3c366b;
                    position: relative;
                }

                .items-table th::after {
                    content: '';
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 1px;
                    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
                }

                .items-table td {
                    padding: 5mm 8mm;
                    border-bottom: 1px solid #e2e8f0;
                    text-align: right;
                    font-weight: 500;
                    font-size: 0.9rem;
                    vertical-align: middle;
                }

                .items-table tr:nth-child(even) {
                    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
                }

                .items-table tr:hover {
                    background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e0 100%);
                    transform: scale(1.01);
                }
                
                .item-name {
                    font-weight: 600;
                    color: #2d3748;
                }
                
                .price {
                    font-weight: 600;
                    color: #2b6cb0;
                }
                
                .total-section {
                    padding: 10mm 20mm;
                    background: white;
                    page-break-inside: avoid;
                }

                .totals-table {
                    width: 100%;
                    max-width: 80mm;
                    margin-left: auto;
                    border-collapse: collapse;
                    border-radius: 8px;
                    overflow: hidden;
                    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                }

                .totals-table td {
                    padding: 4mm 6mm;
                    border-bottom: 1px solid #e2e8f0;
                    font-size: 0.9rem;
                }

                .totals-table .label {
                    font-weight: 600;
                    color: #4a5568;
                    text-align: right;
                    background: #f8fafc;
                }

                .totals-table .amount {
                    font-weight: 700;
                    color: #2d3748;
                    text-align: left;
                    background: white;
                }

                .total-row {
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    font-size: 1.1rem;
                    font-weight: bold;
                }

                .total-row td {
                    border-bottom: none;
                    padding: 6mm;
                }
                
                .notes-section {
                    padding: 8mm 20mm;
                    background: #f7fafc;
                    border-top: 1px solid #e2e8f0;
                    page-break-inside: avoid;
                }

                .notes-section h4 {
                    color: #667eea;
                    margin-bottom: 4mm;
                    font-size: 1rem;
                }

                .notes-text {
                    color: #4a5568;
                    font-style: italic;
                    font-size: 0.9rem;
                    line-height: 1.4;
                }
                
                .invoice-footer {
                    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
                    color: white;
                    padding: 12mm 20mm;
                    text-align: center;
                    position: relative;
                    overflow: hidden;
                    page-break-inside: avoid;
                    margin-top: auto;
                }

                .invoice-footer::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 3px;
                    background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #667eea);
                    background-size: 200% 100%;
                    animation: gradient 3s ease infinite;
                }

                @keyframes gradient {
                    0%, 100% { background-position: 0% 50%; }
                    50% { background-position: 100% 50%; }
                }

                .footer-content {
                    position: relative;
                    z-index: 1;
                }

                .thank-you {
                    font-size: 1.2rem;
                    font-weight: 700;
                    margin-bottom: 6mm;
                    color: #f7fafc;
                }

                .footer-text {
                    opacity: 0.9;
                    font-size: 0.9rem;
                    margin-bottom: 6mm;
                    line-height: 1.5;
                }

                .developer-info {
                    margin-top: 8mm;
                    padding-top: 8mm;
                    border-top: 1px solid rgba(255,255,255,0.2);
                    font-size: 0.8rem;
                    opacity: 0.8;
                }

                .developer-info .dev-title {
                    font-weight: 600;
                    color: #90cdf4;
                    margin-bottom: 2mm;
                }

                .developer-info .dev-name {
                    font-weight: 700;
                    color: #f093fb;
                    font-size: 0.9rem;
                }
                
                @media print {
                    body {
                        padding: 0;
                        margin: 0;
                        background: white !important;
                        -webkit-print-color-adjust: exact;
                        print-color-adjust: exact;
                    }

                    .invoice-container {
                        border: none;
                        border-radius: 0;
                        box-shadow: none;
                        margin: 0;
                        width: 100%;
                        max-width: none;
                        min-height: auto;
                    }

                    .invoice-container::before {
                        display: none;
                    }

                    .invoice-header {
                        border-radius: 0;
                        page-break-after: avoid;
                    }

                    .invoice-info {
                        page-break-inside: avoid;
                    }

                    .items-section {
                        page-break-inside: avoid;
                        margin: 5mm 0;
                    }

                    .total-section {
                        page-break-inside: avoid;
                    }

                    .invoice-footer {
                        page-break-inside: avoid;
                        margin-top: 0;
                    }

                    /* إخفاء عناصر غير ضرورية للطباعة */
                    .no-print {
                        display: none !important;
                    }
                }
            </style>
        </head>
        <body>
            <div class="invoice-container">
                <!-- رأس الفاتورة -->
                <div class="invoice-header">
                    <div class="company-logo">
                        🏢
                    </div>
                    <div class="header-decoration">
                        🏠
                    </div>
                    <div class="header-decoration header-decoration-left">
                        ✨
                    </div>
                    <div class="store-info">
                        <div class="store-name">${settings.storeName || 'مصطفي كشاف للمفروشات'}</div>
                        <div class="store-details">
                            📍 ${settings.storeAddress || 'القاهرة، مصر الجديدة، شارع الحجاز'}<br>
                            📞 هاتف: ${settings.storePhone || '02-12345678'}<br>
                            📧 ${settings.storeEmail || '<EMAIL>'}
                        </div>
                    </div>
                </div>
                
                <!-- معلومات الفاتورة والعميل -->
                <div class="invoice-info">
                    <div class="invoice-details">
                        <h3>تفاصيل الفاتورة</h3>
                        <div class="info-row">
                            <span class="label">رقم الفاتورة:</span>
                            <span class="value invoice-number">${invoice.invoiceNumber}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">التاريخ:</span>
                            <span class="value">${new Date(invoice.createdAt).toLocaleDateString('ar-EG')}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">الوقت:</span>
                            <span class="value">${new Date(invoice.createdAt).toLocaleTimeString('ar-EG')}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">طريقة الدفع:</span>
                            <span class="value">${invoice.paymentMethod}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">الموظف:</span>
                            <span class="value">${invoice.createdBy}</span>
                        </div>
                    </div>
                    
                    <div class="customer-details">
                        <h3>بيانات العميل</h3>
                        <div class="info-row">
                            <span class="label">الاسم:</span>
                            <span class="value">${invoice.customerName}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">الهاتف:</span>
                            <span class="value">${invoice.customerPhone}</span>
                        </div>
                        ${invoice.customerAddress ? `
                            <div class="info-row">
                                <span class="label">العنوان:</span>
                                <span class="value">${invoice.customerAddress}</span>
                            </div>
                        ` : ''}
                    </div>
                </div>
                
                <!-- جدول المنتجات -->
                <div class="items-section">
                    <div class="items-header">
                        المنتجات المطلوبة
                    </div>
                    <table class="items-table">
                        <thead>
                            <tr>
                                <th>🛋️ المنتج</th>
                                <th>💰 السعر</th>
                                <th>📦 الكمية</th>
                                <th>💵 الإجمالي</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${invoice.items.map((item, index) => `
                                <tr>
                                    <td class="item-name">
                                        <strong>${item.productName}</strong>
                                    </td>
                                    <td class="price">${item.price.toLocaleString()} جنيه</td>
                                    <td style="text-align: center; font-weight: 700; color: #667eea;">${item.quantity}</td>
                                    <td class="price" style="font-weight: 700; color: #38a169;">${item.total.toLocaleString()} جنيه</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
                
                <!-- الحسابات -->
                <div class="total-section">
                    <table class="totals-table">
                        <tr>
                            <td class="label">المجموع الفرعي:</td>
                            <td class="amount">${invoice.subtotal.toLocaleString()} جنيه</td>
                        </tr>
                        ${invoice.discount > 0 ? `
                            <tr>
                                <td class="label">الخصم:</td>
                                <td class="amount">-${invoice.discount.toLocaleString()} جنيه</td>
                            </tr>
                        ` : ''}
                        ${invoice.taxAmount > 0 ? `
                            <tr>
                                <td class="label">الضريبة (${invoice.taxRate}%):</td>
                                <td class="amount">${invoice.taxAmount.toLocaleString()} جنيه</td>
                            </tr>
                        ` : ''}
                        <tr class="total-row">
                            <td class="label">المجموع الإجمالي:</td>
                            <td class="amount">${invoice.total.toLocaleString()} جنيه</td>
                        </tr>
                    </table>
                </div>
                
                <!-- الملاحظات -->
                ${invoice.notes ? `
                    <div class="notes-section">
                        <h4>ملاحظات:</h4>
                        <p class="notes-text">${invoice.notes}</p>
                    </div>
                ` : ''}
                
                <!-- تذييل الفاتورة -->
                <div class="invoice-footer">
                    <div class="footer-content">
                        <div class="thank-you">🙏 شكراً لتعاملكم معنا 🙏</div>
                        <div class="footer-text">
                            تم إنشاء هذه الفاتورة بواسطة نظام مصطفي كشاف للمفروشات الاحترافي<br>
                            📅 تاريخ الطباعة: ${currentDate} - ⏰ ${currentTime}
                        </div>
                        <div class="developer-info">
                            <div class="dev-title">💻 تم التطوير والتصميم بواسطة</div>
                            <div class="dev-name">عمرو السيد اليماني</div>
                            <div style="font-size: 0.8rem; margin-top: 5px; opacity: 0.7;">
                                🚀 نظام إدارة مبيعات متطور ومتميز
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </body>
        </html>
        `;
    }

    // طباعة الفاتورة
    async printInvoice(invoice) {
        try {
            const htmlContent = this.generateInvoiceTemplate(invoice);
            const result = await ipcRenderer.invoke('print-invoice', htmlContent);

            if (result) {
                console.log('تم طباعة الفاتورة بنجاح');

                // تسجيل عملية الطباعة
                if (typeof activityLogger !== 'undefined') {
                    activityLogger.logInvoicePrint(invoice);
                }

                return true;
            } else {
                console.error('فشل في طباعة الفاتورة');
                return false;
            }
        } catch (error) {
            console.error('خطأ في الطباعة:', error);

            // تسجيل الخطأ
            if (typeof activityLogger !== 'undefined') {
                activityLogger.logError(error, { context: 'print_invoice', invoiceId: invoice.id });
            }

            return false;
        }
    }

    // معاينة الفاتورة قبل الطباعة
    previewInvoice(invoice) {
        const htmlContent = this.generateInvoiceTemplate(invoice);
        const previewWindow = window.open('', '_blank', 'width=800,height=600');
        previewWindow.document.write(htmlContent);
        previewWindow.document.close();
    }
}

const printManager = new PrintManager();

// دالة طباعة الفاتورة (يتم استدعاؤها من ملفات أخرى)
async function generateInvoicePrint(invoice) {
    const result = await printManager.printInvoice(invoice);
    if (!result) {
        // في حالة فشل الطباعة، عرض معاينة
        printManager.previewInvoice(invoice);
    }
}

// دالة معاينة الفاتورة
function previewInvoice(invoice) {
    printManager.previewInvoice(invoice);
}
