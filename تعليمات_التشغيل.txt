برنامج مصطفي كشاف للمفروشات - نظام إدارة المبيعات
=====================================================

تعليمات التشغيل السريعة:
========================

1. تشغيل البرنامج:
   - انقر نقراً مزدوجاً على ملف "start.bat"
   - أو افتح Command Prompt واكتب: npm start

2. تسجيل الدخول الأول:
   - اسم المستخدم: admin
   - كلمة المرور: 123456

3. الاستخدام الأساسي:
   - لوحة التحكم: عرض الإحصائيات والتنبيهات
   - المنتجات: إضافة وإدارة منتجات المعرض
   - العملاء: إدارة قاعدة بيانات العملاء
   - الفواتير: إنشاء وطباعة الفواتير
   - التقارير: عرض تقارير المبيعات
   - الإعدادات: تخصيص معلومات المعرض

4. إنشاء فاتورة جديدة:
   - اذهب إلى قسم "الفواتير"
   - انقر "فاتورة جديدة"
   - اختر العميل أو أدخل بياناته
   - أضف المنتجات المطلوبة
   - احسب الضرائب والخصومات
   - احفظ واطبع الفاتورة

5. إضافة منتج جديد:
   - اذهب إلى قسم "المنتجات"
   - انقر "إضافة منتج جديد"
   - أدخل تفاصيل المنتج
   - احفظ المنتج

6. النسخ الاحتياطية:
   - يتم حفظ البيانات في مجلد "data"
   - انسخ هذا المجلد بانتظام كنسخة احتياطية

7. استكشاف الأخطاء:
   - تأكد من تثبيت Node.js
   - تأكد من وجود اتصال بالإنترنت عند التثبيت الأول
   - في حالة وجود مشاكل، احذف مجلد "node_modules" وشغل "npm install"

8. الدعم الفني:
   - للمساعدة اتصل على: 02-12345678
   - البريد الإلكتروني: <EMAIL>

ملاحظات مهمة:
==============
- البرنامج يعمل بدون إنترنت بعد التثبيت
- جميع البيانات محفوظة محلياً على الجهاز
- يدعم Windows 7 32-bit وما أحدث
- يمكن إنشاء حسابات متعددة للموظفين

تم تطوير هذا البرنامج خصيصاً لمعرض مصطفي كشاف للمفروشات
جميع الحقوق محفوظة © 2024
