# برنامج مصطفي كشاف للمفروشات - نظام إدارة المبيعات

برنامج مبيعات احترافي مصمم خصيصاً لمعرض مصطفي كشاف للمفروشات. يعمل البرنامج بدون إنترنت ويدعم Windows 7 32-bit.

## المميزات الرئيسية

### 🔐 نظام تسجيل الدخول الآمن
- تسجيل دخول بأسماء مستخدمين وكلمات مرور
- إنشاء حسابات جديدة للموظفين
- حفظ جلسات المستخدمين
- حساب المدير الافتراضي: `admin` / `123456`

### 📦 إدارة المنتجات
- إضافة وتعديل وحذف المنتجات
- تصنيف المنتجات (كنب، طاولات، كراسي، إلخ)
- تتبع المخزون والكميات
- تنبيهات المخزون المنخفض
- حساب التكلفة والربح

### 👥 إدارة العملاء
- قاعدة بيانات شاملة للعملاء
- معلومات الاتصال والعناوين
- تاريخ التعاملات
- بحث سريع في العملاء

### 🧾 نظام الفواتير المتقدم
- إنشاء فواتير احترافية
- حساب الضرائب والخصومات تلقائياً
- طرق دفع متعددة
- ترقيم تلقائي للفواتير
- طباعة فواتير أنيقة ومتميزة

### 📊 لوحة التحكم والتقارير
- إحصائيات المبيعات والأرباح
- تنبيهات المخزون
- آخر الفواتير
- إجراءات سريعة

### 🖨️ نظام الطباعة الاحترافي
- تصميم فواتير أنيق وعصري
- معاينة قبل الطباعة
- دعم طابعات مختلفة
- تخصيص قوالب الطباعة

## متطلبات النظام

- **نظام التشغيل**: Windows 7 32-bit أو أحدث
- **المعالج**: Intel Pentium 4 أو أحدث
- **الذاكرة**: 2 GB RAM كحد أدنى
- **مساحة القرص**: 500 MB مساحة فارغة
- **الشاشة**: دقة 1024x768 كحد أدنى

## التثبيت والتشغيل

### 1. تثبيت Node.js
قم بتحميل وتثبيت Node.js من الموقع الرسمي:
```
https://nodejs.org/
```

### 2. تثبيت التبعيات
افتح Command Prompt في مجلد البرنامج وقم بتشغيل:
```bash
npm install
```

### 3. تشغيل البرنامج
```bash
npm start
```

### 4. بناء التطبيق للتوزيع
```bash
npm run dist
```

## بيانات تسجيل الدخول الافتراضية

- **اسم المستخدم**: `admin`
- **كلمة المرور**: `123456`

## هيكل الملفات

```
├── main.js                 # الملف الرئيسي للتطبيق
├── package.json            # إعدادات المشروع
├── src/
│   ├── index.html         # الواجهة الرئيسية
│   ├── css/
│   │   └── style.css      # ملفات التصميم
│   └── js/
│       ├── app.js         # منطق التطبيق الأساسي
│       ├── auth.js        # نظام المصادقة
│       ├── storage.js     # إدارة البيانات
│       ├── products.js    # إدارة المنتجات
│       ├── customers.js   # إدارة العملاء
│       ├── invoice.js     # إدارة الفواتير
│       └── print.js       # نظام الطباعة
├── data/                  # ملفات البيانات (JSON)
│   ├── users.json        # بيانات المستخدمين
│   ├── products.json     # بيانات المنتجات
│   ├── customers.json    # بيانات العملاء
│   ├── invoices.json     # بيانات الفواتير
│   └── settings.json     # إعدادات النظام
└── assets/               # الصور والملفات المساعدة
```

## استخدام البرنامج

### 1. تسجيل الدخول
- استخدم بيانات المدير الافتراضية أو أنشئ حساب جديد
- يمكن إنشاء حسابات متعددة للموظفين

### 2. إدارة المنتجات
- اذهب إلى قسم "المنتجات"
- أضف منتجات جديدة مع الأسعار والكميات
- صنف المنتجات حسب النوع

### 3. إدارة العملاء
- سجل بيانات العملاء في قسم "العملاء"
- احفظ معلومات الاتصال والعناوين

### 4. إنشاء الفواتير
- اذهب إلى قسم "الفواتير"
- اختر "فاتورة جديدة"
- اختر العميل وأضف المنتجات
- احسب الضرائب والخصومات
- احفظ واطبع الفاتورة

### 5. عرض التقارير
- راجع لوحة التحكم للإحصائيات
- تابع تنبيهات المخزون
- راجع آخر الفواتير

## النسخ الاحتياطية

يقوم البرنامج بإنشاء نسخ احتياطية تلقائية في مجلد `data/backups/`

## الدعم الفني

للدعم الفني أو الاستفسارات:
- البريد الإلكتروني: <EMAIL>
- الهاتف: 02-12345678

## الترخيص

هذا البرنامج مطور خصيصاً لمعرض مصطفي كشاف للمفروشات.
جميع الحقوق محفوظة © 2024

---

**ملاحظة**: تأكد من عمل نسخة احتياطية من ملفات البيانات بانتظام لضمان عدم فقدان المعلومات المهمة.
