// إدارة الفواتير
class InvoiceManager {
    constructor() {
        this.invoices = [];
        this.currentInvoice = null;
        this.loadInvoices();
    }

    loadInvoices() {
        this.invoices = storage.readFile('invoices.json') || [];
    }

    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    }

    generateInvoiceNumber() {
        const settings = storage.readFile('settings.json') || {};
        const prefix = settings.invoicePrefix || 'INV-';
        const nextNumber = settings.nextInvoiceNumber || 1001;
        
        // تحديث رقم الفاتورة التالي
        settings.nextInvoiceNumber = nextNumber + 1;
        storage.writeFile('settings.json', settings);
        
        return prefix + nextNumber;
    }

    createInvoice(invoiceData) {
        const invoice = {
            id: this.generateId(),
            invoiceNumber: this.generateInvoiceNumber(),
            customerId: invoiceData.customerId,
            customerName: invoiceData.customerName,
            customerPhone: invoiceData.customerPhone,
            customerAddress: invoiceData.customerAddress || '',
            items: invoiceData.items || [],
            subtotal: invoiceData.subtotal || 0,
            taxRate: invoiceData.taxRate || 0,
            taxAmount: invoiceData.taxAmount || 0,
            discount: invoiceData.discount || 0,
            total: invoiceData.total || 0,
            profit: invoiceData.profit || 0,
            paymentMethod: invoiceData.paymentMethod || 'نقدي',
            notes: invoiceData.notes || '',
            status: 'مدفوعة',
            createdAt: new Date().toISOString(),
            createdBy: authManager.getCurrentUser()?.fullName || 'غير محدد'
        };

        if (storage.addItem('invoices.json', invoice)) {
            // تحديث المخزون
            this.updateInventory(invoice.items);
            this.loadInvoices();

            // تسجيل العملية
            if (typeof activityLogger !== 'undefined') {
                activityLogger.logInvoiceCreate(invoice);
            }

            return { success: true, invoice };
        }
        return { success: false, message: 'فشل في إنشاء الفاتورة' };
    }

    updateInventory(items) {
        items.forEach(item => {
            productManager.updateStock(item.productId, item.quantity);
        });
    }

    updateInvoice(id, invoiceData) {
        const existingInvoice = this.getInvoiceById(id);
        if (!existingInvoice) {
            return { success: false, message: 'الفاتورة غير موجودة' };
        }

        const updatedInvoice = {
            ...existingInvoice,
            customerId: invoiceData.customerId,
            customerName: invoiceData.customerName,
            customerPhone: invoiceData.customerPhone,
            customerAddress: invoiceData.customerAddress || '',
            items: invoiceData.items || [],
            subtotal: invoiceData.subtotal || 0,
            taxRate: invoiceData.taxRate || 0,
            taxAmount: invoiceData.taxAmount || 0,
            discount: invoiceData.discount || 0,
            total: invoiceData.total || 0,
            profit: invoiceData.profit || 0,
            paymentMethod: invoiceData.paymentMethod || 'نقدي',
            notes: invoiceData.notes || '',
            updatedAt: new Date().toISOString(),
            updatedBy: authManager.getCurrentUser()?.fullName || 'غير محدد'
        };

        if (storage.updateItem('invoices.json', id, updatedInvoice)) {
            // تحديث المخزون
            this.updateInventory(updatedInvoice.items);
            this.loadInvoices();
            return { success: true, invoice: updatedInvoice };
        }
        return { success: false, message: 'فشل في تحديث الفاتورة' };
    }

    deleteInvoice(id) {
        if (storage.deleteItem('invoices.json', id)) {
            this.loadInvoices();
            return { success: true };
        }
        return { success: false, message: 'فشل في حذف الفاتورة' };
    }

    getInvoiceById(id) {
        return storage.getItemById('invoices.json', id);
    }

    searchInvoices(searchTerm) {
        return storage.searchItems('invoices.json', searchTerm, ['invoiceNumber', 'customerName', 'customerPhone']);
    }
}

const invoiceManager = new InvoiceManager();

// تحميل صفحة الفواتير
function loadInvoices() {
    const invoices = invoiceManager.invoices;
    const invoicesHTML = `
        <div class="invoices-container">
            <div class="search-bar">
                <div class="search-input-group">
                    <i class="fas fa-search"></i>
                    <input type="text" id="invoiceSearch" placeholder="البحث في الفواتير..." onkeyup="searchInvoices()">
                </div>
            </div>

            <div class="invoices-table">
                <table>
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>العميل</th>
                            <th>التاريخ</th>
                            <th>المبلغ الإجمالي</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody id="invoicesTableBody">
                        ${invoices.map(invoice => `
                            <tr data-id="${invoice.id}">
                                <td>
                                    <strong class="invoice-number">${invoice.invoiceNumber}</strong>
                                </td>
                                <td>
                                    <div class="customer-info">
                                        <div class="customer-name">${invoice.customerName}</div>
                                        <div class="customer-phone">${invoice.customerPhone}</div>
                                    </div>
                                </td>
                                <td>${new Date(invoice.createdAt).toLocaleDateString('ar-EG')}</td>
                                <td>
                                    <span class="amount">${invoice.total.toLocaleString()} جنيه</span>
                                </td>
                                <td>
                                    <span class="status paid">${invoice.status}</span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <button class="btn-icon view" onclick="viewInvoice('${invoice.id}')" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn-icon edit" onclick="editInvoice('${invoice.id}')" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="btn-icon print" onclick="printInvoice('${invoice.id}')" title="طباعة">
                                            <i class="fas fa-print"></i>
                                        </button>
                                        <button class="btn-icon delete" onclick="deleteInvoice('${invoice.id}')" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
                
                ${invoices.length === 0 ? '<div class="no-data">لا توجد فواتير</div>' : ''}
            </div>
        </div>

        <!-- نافذة إنشاء فاتورة جديدة -->
        <div id="newInvoiceModal" class="modal large-modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>إنشاء فاتورة جديدة</h2>
                    <button class="close-btn" onclick="closeNewInvoiceModal()">&times;</button>
                </div>
                
                <div class="invoice-form">
                    <!-- معلومات العميل -->
                    <div class="form-section">
                        <h3><i class="fas fa-user"></i> معلومات العميل</h3>
                        <div class="form-row">
                            <div class="form-group">
                                <label>العميل</label>
                                <select id="invoiceCustomer" onchange="selectCustomer()">
                                    <option value="">اختر العميل</option>
                                    ${customerManager.customers.map(customer => 
                                        `<option value="${customer.id}">${customer.name} - ${customer.phone}</option>`
                                    ).join('')}
                                </select>
                            </div>
                            <div class="form-group">
                                <label>اسم العميل</label>
                                <input type="text" id="customerName" required>
                            </div>
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label>رقم الهاتف</label>
                                <input type="tel" id="customerPhone" required>
                            </div>
                            <div class="form-group">
                                <label>العنوان</label>
                                <input type="text" id="customerAddress">
                            </div>
                        </div>
                    </div>

                    <!-- المنتجات -->
                    <div class="form-section products-section">
                        <div class="section-header">
                            <h3><i class="fas fa-shopping-cart"></i> المنتجات</h3>
                            <div class="products-summary">
                                <span class="items-count">عدد الأصناف: <strong id="itemsCount">0</strong></span>
                                <span class="total-quantity">إجمالي الكمية: <strong id="totalQuantity">0</strong></span>
                            </div>
                        </div>

                        <div class="product-selector-container">
                            <div class="selector-header">
                                <i class="fas fa-plus-circle"></i>
                                <span>إضافة منتج جديد</span>
                            </div>
                            <div class="product-grid" id="productGrid">
                                ${productManager.products.length > 0 ?
                                    productManager.products.map(product => `
                                        <div class="product-card ${product.stock === 0 ? 'out-of-stock' : product.stock <= 5 ? 'low-stock' : ''}"
                                             onclick="addProductToInvoiceFromCard('${product.id}')"
                                             data-id="${product.id}">
                                            <div class="product-image">
                                                <i class="fas fa-couch"></i>
                                            </div>
                                            <div class="product-info">
                                                <h4 class="product-name">${product.name}</h4>
                                                <p class="product-category">${product.category}</p>
                                                <div class="product-price">${product.price.toLocaleString()} جنيه</div>
                                                <div class="product-stock ${product.stock <= 5 ? 'low' : ''}">
                                                    <i class="fas fa-boxes"></i>
                                                    متوفر: ${product.stock} قطعة
                                                </div>
                                            </div>
                                            <div class="add-button">
                                                <i class="fas fa-plus"></i>
                                            </div>
                                            ${product.stock === 0 ? '<div class="out-of-stock-overlay">نفد من المخزون</div>' : ''}
                                        </div>
                                    `).join('') :
                                    `<div class="no-products">
                                        <i class="fas fa-box-open"></i>
                                        <h4>لا توجد منتجات متاحة</h4>
                                        <p>يرجى إضافة منتجات أولاً من قسم المنتجات</p>
                                        <button type="button" class="btn btn-primary" onclick="app.loadPage('products')">
                                            <i class="fas fa-plus"></i> إضافة منتجات
                                        </button>
                                    </div>`
                                }
                            </div>
                        </div>

                        <div class="selected-items" id="selectedItems" style="display: none;">
                            <div class="items-header">
                                <h4><i class="fas fa-list"></i> المنتجات المحددة</h4>
                                <button type="button" class="clear-all-btn" onclick="clearAllItems()">
                                    <i class="fas fa-trash"></i> مسح الكل
                                </button>
                            </div>
                            <div class="items-list" id="itemsList">
                                <!-- سيتم إضافة المنتجات المحددة هنا -->
                            </div>
                        </div>
                    </div>

                    <!-- الحسابات -->
                    <div class="form-section calculations-section">
                        <h3><i class="fas fa-calculator"></i> ملخص الفاتورة</h3>
                        <div class="calculations-grid">
                            <div class="calc-card subtotal">
                                <div class="calc-icon">
                                    <i class="fas fa-list"></i>
                                </div>
                                <div class="calc-details">
                                    <label>المجموع الفرعي</label>
                                    <span class="amount" id="subtotalAmount">0.00 جنيه</span>
                                </div>
                            </div>

                            <div class="calc-card discount">
                                <div class="calc-icon">
                                    <i class="fas fa-percentage"></i>
                                </div>
                                <div class="calc-details">
                                    <label>الخصم</label>
                                    <div class="discount-input">
                                        <input type="number" id="discountAmount" value="0" min="0" onchange="calculateTotal()" placeholder="0">
                                        <span>جنيه</span>
                                    </div>
                                </div>
                            </div>

                            <div class="calc-card total">
                                <div class="calc-icon">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                                <div class="calc-details">
                                    <label>المجموع الإجمالي</label>
                                    <span class="amount total-amount" id="totalAmount">0.00 جنيه</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ملاحظات وطريقة الدفع -->
                    <div class="form-section">
                        <div class="form-row">
                            <div class="form-group">
                                <label>طريقة الدفع</label>
                                <select id="paymentMethod">
                                    <option value="نقدي">نقدي</option>
                                    <option value="بطاقة ائتمان">بطاقة ائتمان</option>
                                    <option value="تحويل بنكي">تحويل بنكي</option>
                                    <option value="شيك">شيك</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label>ملاحظات</label>
                                <textarea id="invoiceNotes" rows="3"></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" class="btn btn-primary action-btn" onclick="saveInvoice()">
                            <i class="fas fa-save"></i>
                            <span>حفظ الفاتورة</span>
                        </button>
                        <button type="button" class="btn btn-success action-btn" onclick="saveAndPrintInvoice()">
                            <i class="fas fa-print"></i>
                            <span>حفظ وطباعة</span>
                        </button>
                        <button type="button" class="btn btn-secondary action-btn" onclick="closeNewInvoiceModal()">
                            <i class="fas fa-times"></i>
                            <span>إلغاء</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('invoicesContent').innerHTML = invoicesHTML;
    addInvoiceStyles();
}

// متغيرات الفاتورة الحالية
let currentInvoiceItems = [];
let currentSubtotal = 0;

// إضافة منتج للفاتورة من البطاقة
function addProductToInvoiceFromCard(productId) {
    const product = productManager.getProductById(productId);

    if (!product) {
        alert('المنتج غير موجود');
        return;
    }

    if (product.stock === 0) {
        alert('هذا المنتج غير متوفر في المخزون');
        return;
    }

    // التحقق من وجود المنتج في الفاتورة
    const existingItem = currentInvoiceItems.find(item => item.productId === productId);

    if (existingItem) {
        if (existingItem.quantity < product.stock) {
            existingItem.quantity++;
            existingItem.total = existingItem.quantity * existingItem.price;
        } else {
            alert('لا يمكن إضافة كمية أكثر من المتوفر في المخزون');
            return;
        }
    } else {
        currentInvoiceItems.push({
            productId: productId,
            productName: product.name,
            price: product.price,
            quantity: 1,
            total: product.price
        });
    }

    updateSelectedItems();
    calculateTotal();
    updateProductsSummary();

    // تأثير بصري للبطاقة
    const card = document.querySelector(`[data-id="${productId}"]`);
    if (card) {
        card.classList.add('added');
        setTimeout(() => card.classList.remove('added'), 300);

        // إضافة تأثير صوتي بصري
        const addButton = card.querySelector('.add-button');
        if (addButton) {
            addButton.innerHTML = '<i class="fas fa-check"></i>';
            setTimeout(() => {
                addButton.innerHTML = '<i class="fas fa-plus"></i>';
            }, 1000);
        }
    }
}

// تحديث قائمة المنتجات المحددة
function updateSelectedItems() {
    const selectedItemsContainer = document.getElementById('selectedItems');
    const itemsList = document.getElementById('itemsList');

    if (currentInvoiceItems.length === 0) {
        selectedItemsContainer.style.display = 'none';
        return;
    }

    selectedItemsContainer.style.display = 'block';

    itemsList.innerHTML = currentInvoiceItems.map((item, index) => `
        <div class="selected-item" data-index="${index}">
            <div class="item-image">
                <i class="fas fa-couch"></i>
            </div>
            <div class="item-details">
                <h5 class="item-name">${item.productName}</h5>
                <div class="item-price">${item.price.toLocaleString()} جنيه للقطعة</div>
            </div>
            <div class="quantity-section">
                <label>الكمية:</label>
                <div class="quantity-controls">
                    <button type="button" class="qty-btn minus" onclick="changeQuantity(${index}, -1)">
                        <i class="fas fa-minus"></i>
                    </button>
                    <input type="number" class="qty-input" value="${item.quantity}" min="1"
                           onchange="setQuantity(${index}, this.value)" readonly>
                    <button type="button" class="qty-btn plus" onclick="changeQuantity(${index}, 1)">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
            <div class="item-total">
                <div class="total-label">الإجمالي:</div>
                <div class="total-amount">${item.total.toLocaleString()} جنيه</div>
            </div>
            <button type="button" class="remove-item-btn" onclick="removeItem(${index})" title="إزالة المنتج">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `).join('');
}

// تغيير الكمية
function changeQuantity(index, change) {
    const item = currentInvoiceItems[index];
    const product = productManager.getProductById(item.productId);

    const newQuantity = item.quantity + change;

    if (newQuantity <= 0) {
        removeItem(index);
        return;
    }

    if (newQuantity > product.stock) {
        alert('لا يمكن إضافة كمية أكثر من المتوفر في المخزون');
        return;
    }

    item.quantity = newQuantity;
    item.total = item.quantity * item.price;

    updateSelectedItems();
    calculateTotal();
    updateProductsSummary();
}

// تعيين كمية محددة
function setQuantity(index, quantity) {
    const item = currentInvoiceItems[index];
    const product = productManager.getProductById(item.productId);
    const newQuantity = parseInt(quantity) || 1;

    if (newQuantity > product.stock) {
        alert('لا يمكن إضافة كمية أكثر من المتوفر في المخزون');
        return;
    }

    item.quantity = newQuantity;
    item.total = item.quantity * item.price;

    updateSelectedItems();
    calculateTotal();
    updateProductsSummary();
}

// إزالة عنصر من الفاتورة
function removeItem(index) {
    currentInvoiceItems.splice(index, 1);
    updateSelectedItems();
    calculateTotal();
    updateProductsSummary();
}

// مسح جميع المنتجات
function clearAllItems() {
    if (currentInvoiceItems.length === 0) return;

    if (confirm('هل أنت متأكد من مسح جميع المنتجات؟')) {
        currentInvoiceItems = [];
        updateSelectedItems();
        calculateTotal();
        updateProductsSummary();
    }
}

// تحديث ملخص المنتجات
function updateProductsSummary() {
    const itemsCount = currentInvoiceItems.length;
    const totalQuantity = currentInvoiceItems.reduce((sum, item) => sum + item.quantity, 0);

    document.getElementById('itemsCount').textContent = itemsCount;
    document.getElementById('totalQuantity').textContent = totalQuantity;
}

// حساب الإجمالي
function calculateTotal() {
    currentSubtotal = currentInvoiceItems.reduce((sum, item) => sum + item.total, 0);

    const discount = parseFloat(document.getElementById('discountAmount').value) || 0;
    const total = currentSubtotal - discount;

    document.getElementById('subtotalAmount').textContent = currentSubtotal.toLocaleString() + ' جنيه';
    document.getElementById('totalAmount').textContent = total.toLocaleString() + ' جنيه';
}

// اختيار عميل
function selectCustomer() {
    const customerId = document.getElementById('invoiceCustomer').value;
    if (customerId) {
        const customer = customerManager.getCustomerById(customerId);
        if (customer) {
            document.getElementById('customerName').value = customer.name;
            document.getElementById('customerPhone').value = customer.phone;
            document.getElementById('customerAddress').value = customer.address || '';
        }
    } else {
        document.getElementById('customerName').value = '';
        document.getElementById('customerPhone').value = '';
        document.getElementById('customerAddress').value = '';
    }
}

// حفظ الفاتورة
function saveInvoice() {
    if (!validateInvoiceData()) return;

    const editId = document.getElementById('newInvoiceModal').getAttribute('data-edit-id');
    const invoiceData = getInvoiceData();

    let result;
    if (editId) {
        // تحديث الفاتورة الموجودة
        result = invoiceManager.updateInvoice(editId, invoiceData);
    } else {
        // إنشاء فاتورة جديدة
        result = invoiceManager.createInvoice(invoiceData);
    }

    if (result.success) {
        alert(editId ? 'تم تحديث الفاتورة بنجاح' : 'تم حفظ الفاتورة بنجاح');
        closeNewInvoiceModal();
        loadInvoices();
    } else {
        alert(result.message);
    }
}

// حفظ وطباعة الفاتورة
function saveAndPrintInvoice() {
    if (!validateInvoiceData()) return;

    const invoiceData = getInvoiceData();
    const result = invoiceManager.createInvoice(invoiceData);

    if (result.success) {
        alert('تم حفظ الفاتورة بنجاح');
        closeNewInvoiceModal();
        loadInvoices();
        printInvoice(result.invoice.id);
    } else {
        alert(result.message);
    }
}

// التحقق من صحة بيانات الفاتورة
function validateInvoiceData() {
    const customerName = document.getElementById('customerName').value.trim();
    const customerPhone = document.getElementById('customerPhone').value.trim();

    if (!customerName) {
        alert('يرجى إدخال اسم العميل');
        return false;
    }

    if (!customerPhone) {
        alert('يرجى إدخال رقم هاتف العميل');
        return false;
    }

    if (currentInvoiceItems.length === 0) {
        alert('يرجى إضافة منتج واحد على الأقل');
        return false;
    }

    return true;
}

// الحصول على بيانات الفاتورة
function getInvoiceData() {
    const discount = parseFloat(document.getElementById('discountAmount').value) || 0;
    const total = currentSubtotal - discount;

    // حساب الربح
    const profit = currentInvoiceItems.reduce((sum, item) => {
        const product = productManager.getProductById(item.productId);
        const itemProfit = (item.price - (product.cost || 0)) * item.quantity;
        return sum + itemProfit;
    }, 0) - discount;

    return {
        customerId: document.getElementById('invoiceCustomer').value,
        customerName: document.getElementById('customerName').value.trim(),
        customerPhone: document.getElementById('customerPhone').value.trim(),
        customerAddress: document.getElementById('customerAddress').value.trim(),
        items: [...currentInvoiceItems],
        subtotal: currentSubtotal,
        taxRate: 0,
        taxAmount: 0,
        discount: discount,
        total: total,
        profit: profit,
        paymentMethod: document.getElementById('paymentMethod').value,
        notes: document.getElementById('invoiceNotes').value.trim()
    };
}

// البحث في الفواتير
function searchInvoices() {
    const searchTerm = document.getElementById('invoiceSearch').value;
    const invoices = searchTerm ? invoiceManager.searchInvoices(searchTerm) : invoiceManager.invoices;

    const tableBody = document.getElementById('invoicesTableBody');
    tableBody.innerHTML = invoices.map(invoice => `
        <tr data-id="${invoice.id}">
            <td>
                <strong class="invoice-number">${invoice.invoiceNumber}</strong>
            </td>
            <td>
                <div class="customer-info">
                    <div class="customer-name">${invoice.customerName}</div>
                    <div class="customer-phone">${invoice.customerPhone}</div>
                </div>
            </td>
            <td>${new Date(invoice.createdAt).toLocaleDateString('ar-EG')}</td>
            <td>
                <span class="amount">${invoice.total.toLocaleString()} جنيه</span>
            </td>
            <td>
                <span class="status paid">${invoice.status}</span>
            </td>
            <td>
                <div class="action-buttons">
                    <button class="btn-icon view" onclick="viewInvoice('${invoice.id}')" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn-icon edit" onclick="editInvoice('${invoice.id}')" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-icon print" onclick="printInvoice('${invoice.id}')" title="طباعة">
                        <i class="fas fa-print"></i>
                    </button>
                    <button class="btn-icon delete" onclick="deleteInvoice('${invoice.id}')" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// عرض الفاتورة
function viewInvoice(id) {
    const invoice = invoiceManager.getInvoiceById(id);
    if (invoice) {
        // سيتم تنفيذ هذه الدالة لاحقاً
        alert('عرض الفاتورة: ' + invoice.invoiceNumber);
    }
}

// تعديل الفاتورة
function editInvoice(id) {
    const invoice = invoiceManager.getInvoiceById(id);
    if (invoice) {
        // إعادة المخزون للمنتجات (إلغاء البيع السابق)
        invoice.items.forEach(item => {
            const product = productManager.getProductById(item.productId);
            if (product) {
                productManager.updateProduct(item.productId, {
                    ...product,
                    stock: product.stock + item.quantity
                });
            }
        });

        // تحميل بيانات الفاتورة في النموذج
        currentInvoiceItems = [...invoice.items];
        currentSubtotal = invoice.subtotal;

        // ملء النموذج
        document.getElementById('invoiceCustomer').value = invoice.customerId || '';
        document.getElementById('customerName').value = invoice.customerName;
        document.getElementById('customerPhone').value = invoice.customerPhone;
        document.getElementById('customerAddress').value = invoice.customerAddress || '';
        document.getElementById('discountAmount').value = invoice.discount || 0;
        document.getElementById('paymentMethod').value = invoice.paymentMethod || 'نقدي';
        document.getElementById('invoiceNotes').value = invoice.notes || '';

        // تحديث الجدول والحسابات
        updateSelectedItems();
        calculateTotal();
        updateProductsSummary();

        // تعيين معرف التعديل
        document.getElementById('newInvoiceModal').setAttribute('data-edit-id', id);
        document.querySelector('#newInvoiceModal h2').textContent = 'تعديل الفاتورة ' + invoice.invoiceNumber;

        // إظهار النافذة
        document.getElementById('newInvoiceModal').style.display = 'flex';
    }
}

// طباعة الفاتورة
function printInvoice(id) {
    const invoice = invoiceManager.getInvoiceById(id);
    if (invoice) {
        // سيتم تنفيذ هذه الدالة في ملف print.js
        if (typeof generateInvoicePrint === 'function') {
            generateInvoicePrint(invoice);
        } else {
            alert('طباعة الفاتورة: ' + invoice.invoiceNumber);
        }
    }
}

// حذف الفاتورة
function deleteInvoice(id) {
    if (confirm('هل أنت متأكد من حذف هذه الفاتورة؟')) {
        const result = invoiceManager.deleteInvoice(id);
        if (result.success) {
            loadInvoices();
            alert('تم حذف الفاتورة بنجاح');
        } else {
            alert(result.message);
        }
    }
}

// إظهار نافذة فاتورة جديدة
function showNewInvoiceModal() {
    currentInvoiceItems = [];
    currentSubtotal = 0;

    document.getElementById('invoiceCustomer').selectedIndex = 0;
    document.getElementById('customerName').value = '';
    document.getElementById('customerPhone').value = '';
    document.getElementById('customerAddress').value = '';
    document.getElementById('discountAmount').value = '0';
    document.getElementById('paymentMethod').selectedIndex = 0;
    document.getElementById('invoiceNotes').value = '';

    // إزالة معرف التعديل وإعادة تعيين العنوان
    document.getElementById('newInvoiceModal').removeAttribute('data-edit-id');
    document.querySelector('#newInvoiceModal h2').textContent = 'إنشاء فاتورة جديدة';

    updateSelectedItems();
    calculateTotal();
    updateProductsSummary();

    document.getElementById('newInvoiceModal').style.display = 'flex';
}

// إغلاق نافذة الفاتورة الجديدة
function closeNewInvoiceModal() {
    document.getElementById('newInvoiceModal').style.display = 'none';
    // إزالة معرف التعديل عند الإغلاق
    document.getElementById('newInvoiceModal').removeAttribute('data-edit-id');
    document.querySelector('#newInvoiceModal h2').textContent = 'إنشاء فاتورة جديدة';
}

// إضافة أنماط الفواتير
function addInvoiceStyles() {
    if (!document.getElementById('invoiceStyles')) {
        const style = document.createElement('style');
        style.id = 'invoiceStyles';
        style.textContent = `
            .invoices-container { padding: 0; }
            .invoices-table { background: white; border-radius: 15px; overflow: hidden; box-shadow: 0 5px 15px rgba(0,0,0,0.08); }
            .invoices-table table { width: 100%; border-collapse: collapse; }
            .invoices-table th { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px; text-align: right; font-weight: 600; }
            .invoices-table td { padding: 15px; border-bottom: 1px solid #eee; }
            .invoices-table tr:hover { background: #f8f9fa; }
            .invoice-number { color: #667eea; font-weight: 600; }
            .customer-info .customer-name { font-weight: 600; color: #333; }
            .customer-info .customer-phone { font-size: 0.9rem; color: #666; }
            .amount { font-weight: 700; color: #2e7d32; }
            .status { padding: 4px 12px; border-radius: 20px; font-size: 0.8rem; font-weight: 600; }
            .status.paid { background: #e8f5e8; color: #2e7d32; }
            .btn-icon.print { background: #fff3e0; color: #f57c00; }

            .large-modal .modal-content { max-width: 1200px; }
            .invoice-form { padding: 25px; }
            .form-section { margin-bottom: 30px; padding: 25px; background: #f8f9fa; border-radius: 15px; border: 1px solid #e2e8f0; }
            .form-section h3 { color: #333; margin-bottom: 20px; display: flex; align-items: center; gap: 10px; font-size: 1.3rem; }
            .form-row { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 15px; }
            .form-group { display: flex; flex-direction: column; }
            .form-group label { margin-bottom: 8px; font-weight: 600; color: #333; }
            .form-group input, .form-group select, .form-group textarea { padding: 12px; border: 2px solid #e1e5e9; border-radius: 8px; font-size: 1rem; }
            .form-group input:focus, .form-group select:focus, .form-group textarea:focus { outline: none; border-color: #667eea; }

            /* تصميم قسم المنتجات المحسن */
            .products-section { background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); border: 2px solid #cbd5e0; }
            .section-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px; }
            .section-header h3 { margin: 0; color: #2d3748; }
            .products-summary { display: flex; gap: 20px; font-size: 0.9rem; }
            .products-summary span { background: white; padding: 8px 12px; border-radius: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }

            .product-selector-container { margin-bottom: 25px; }
            .selector-header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px 20px; border-radius: 10px 10px 0 0; display: flex; align-items: center; gap: 10px; font-weight: 600; }

            .product-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(280px, 1fr)); gap: 15px; padding: 20px; background: white; border-radius: 0 0 10px 10px; max-height: 400px; overflow-y: auto; }
            .product-card { background: white; border: 2px solid #e2e8f0; border-radius: 12px; padding: 15px; cursor: pointer; transition: all 0.3s ease; position: relative; overflow: hidden; }
            .product-card:hover { transform: translateY(-3px); box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15); border-color: #667eea; }
            .product-card.added { transform: scale(0.95); background: #f0fff4; border-color: #38a169; }
            .product-card.out-of-stock { opacity: 0.6; cursor: not-allowed; }
            .product-card.low-stock { border-color: #f59e0b; background: #fffbeb; }

            .product-image { width: 50px; height: 50px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 10px; display: flex; align-items: center; justify-content: center; color: white; font-size: 1.5rem; margin-bottom: 12px; }
            .product-info { flex: 1; }
            .product-name { font-size: 1rem; font-weight: 600; color: #2d3748; margin: 0 0 5px 0; }
            .product-category { font-size: 0.8rem; color: #718096; margin: 0 0 8px 0; }
            .product-price { font-size: 1.1rem; font-weight: 700; color: #2b6cb0; margin-bottom: 8px; }
            .product-stock { font-size: 0.85rem; color: #4a5568; display: flex; align-items: center; gap: 5px; }
            .product-stock.low { color: #d69e2e; font-weight: 600; }
            .add-button { position: absolute; top: 10px; left: 10px; width: 30px; height: 30px; background: #48bb78; color: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 0.9rem; opacity: 0; transition: opacity 0.3s ease; }
            .product-card:hover .add-button { opacity: 1; }
            .out-of-stock-overlay { position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.7); color: white; display: flex; align-items: center; justify-content: center; font-weight: 600; border-radius: 12px; }

            /* قائمة المنتجات المحددة */
            .selected-items { background: white; border-radius: 15px; padding: 20px; border: 2px solid #e2e8f0; }
            .items-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
            .items-header h4 { margin: 0; color: #2d3748; display: flex; align-items: center; gap: 10px; }
            .clear-all-btn { background: #fed7d7; color: #c53030; border: none; padding: 8px 16px; border-radius: 8px; cursor: pointer; transition: all 0.3s ease; }
            .clear-all-btn:hover { background: #feb2b2; }

            .selected-item { display: flex; align-items: center; gap: 15px; padding: 15px; background: #f7fafc; border-radius: 12px; margin-bottom: 15px; border: 1px solid #e2e8f0; transition: all 0.3s ease; }
            .selected-item:hover { box-shadow: 0 4px 12px rgba(0,0,0,0.1); }
            .selected-item .item-image { width: 40px; height: 40px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 8px; display: flex; align-items: center; justify-content: center; color: white; }
            .item-details { flex: 1; }
            .item-name { font-size: 1rem; font-weight: 600; color: #2d3748; margin: 0 0 4px 0; }
            .item-price { font-size: 0.9rem; color: #718096; }
            .quantity-section { display: flex; flex-direction: column; align-items: center; gap: 8px; }
            .quantity-section label { font-size: 0.8rem; color: #4a5568; font-weight: 600; }
            .quantity-controls { display: flex; align-items: center; gap: 8px; }
            .qty-btn { width: 32px; height: 32px; border: 2px solid #e2e8f0; background: white; border-radius: 6px; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease; }
            .qty-btn:hover { background: #f7fafc; border-color: #667eea; }
            .qty-btn.minus:hover { background: #fed7d7; border-color: #e53e3e; }
            .qty-btn.plus:hover { background: #c6f6d5; border-color: #38a169; }
            .qty-input { width: 50px; text-align: center; border: 2px solid #e2e8f0; border-radius: 6px; padding: 6px; font-weight: 600; }
            .item-total { text-align: center; }
            .total-label { font-size: 0.8rem; color: #4a5568; margin-bottom: 4px; }
            .total-amount { font-size: 1rem; font-weight: 700; color: #2b6cb0; }
            .remove-item-btn { width: 32px; height: 32px; background: #fed7d7; color: #c53030; border: none; border-radius: 6px; cursor: pointer; transition: all 0.3s ease; }
            .remove-item-btn:hover { background: #feb2b2; }

            /* قسم الحسابات المحسن */
            .calculations-section { background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%); border: 2px solid #e2e8f0; }
            .calculations-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; }
            .calc-card { background: white; padding: 20px; border-radius: 15px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); border: 2px solid transparent; transition: all 0.3s ease; }
            .calc-card:hover { transform: translateY(-2px); box-shadow: 0 8px 25px rgba(0,0,0,0.15); }
            .calc-card.subtotal { border-color: #3182ce; }
            .calc-card.discount { border-color: #d69e2e; }
            .calc-card.total { border-color: #38a169; background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%); }
            .calc-card { display: flex; align-items: center; gap: 15px; }
            .calc-icon { width: 50px; height: 50px; border-radius: 12px; display: flex; align-items: center; justify-content: center; font-size: 1.3rem; color: white; }
            .calc-card.subtotal .calc-icon { background: linear-gradient(135deg, #3182ce 0%, #2c5282 100%); }
            .calc-card.discount .calc-icon { background: linear-gradient(135deg, #d69e2e 0%, #b7791f 100%); }
            .calc-card.total .calc-icon { background: linear-gradient(135deg, #38a169 0%, #2f855a 100%); }
            .calc-details { flex: 1; }
            .calc-details label { display: block; font-size: 0.9rem; color: #4a5568; font-weight: 600; margin-bottom: 8px; }
            .calc-details .amount { font-size: 1.3rem; font-weight: 700; color: #2d3748; }
            .calc-details .total-amount { font-size: 1.5rem; color: #38a169; }
            .discount-input { display: flex; align-items: center; gap: 8px; }
            .discount-input input { flex: 1; padding: 10px; border: 2px solid #e2e8f0; border-radius: 8px; font-size: 1.1rem; font-weight: 600; text-align: center; }
            .discount-input input:focus { outline: none; border-color: #d69e2e; }
            .discount-input span { font-size: 0.9rem; color: #4a5568; font-weight: 600; }

            .form-actions { display: flex; gap: 15px; justify-content: center; margin-top: 30px; padding: 25px; background: #f7fafc; border-radius: 15px; border: 2px solid #e2e8f0; }
            .action-btn { padding: 15px 30px; border-radius: 12px; font-size: 1.1rem; font-weight: 600; display: flex; align-items: center; gap: 10px; transition: all 0.3s ease; min-width: 160px; justify-content: center; }
            .action-btn:hover { transform: translateY(-2px); box-shadow: 0 8px 25px rgba(0,0,0,0.15); }
            .action-btn i { font-size: 1.2rem; }
            .btn-primary.action-btn { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
            .btn-success.action-btn { background: linear-gradient(135deg, #48bb78 0%, #38a169 100%); }
            .btn-secondary.action-btn { background: linear-gradient(135deg, #a0aec0 0%, #718096 100%); }

            /* رسالة عدم وجود منتجات */
            .no-products { text-align: center; padding: 60px 20px; color: #718096; }
            .no-products i { font-size: 4rem; margin-bottom: 20px; color: #cbd5e0; }
            .no-products h4 { font-size: 1.5rem; margin-bottom: 10px; color: #4a5568; }
            .no-products p { margin-bottom: 25px; font-size: 1.1rem; }
            .no-products .btn { padding: 12px 24px; }
        `;
        document.head.appendChild(style);
    }
}
