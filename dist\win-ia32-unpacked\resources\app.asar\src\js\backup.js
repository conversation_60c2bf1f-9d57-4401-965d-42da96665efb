// نظام النسخ الاحتياطي التلقائي
class BackupManager {
    constructor() {
        this.backupInterval = null;
        this.init();
    }

    init() {
        // بدء النسخ الاحتياطي التلقائي كل ساعة
        this.startAutoBackup();
        
        // نسخة احتياطية عند إغلاق التطبيق
        window.addEventListener('beforeunload', () => {
            this.createBackup('shutdown');
        });
    }

    startAutoBackup() {
        // نسخة احتياطية كل ساعة
        this.backupInterval = setInterval(() => {
            this.createBackup('auto');
        }, 60 * 60 * 1000); // ساعة واحدة
    }

    stopAutoBackup() {
        if (this.backupInterval) {
            clearInterval(this.backupInterval);
        }
    }

    createBackup(type = 'manual') {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupData = {
                timestamp: new Date().toISOString(),
                type: type,
                version: '1.0.0',
                data: {
                    users: storage.readFile('users.json') || [],
                    products: storage.readFile('products.json') || [],
                    customers: storage.readFile('customers.json') || [],
                    invoices: storage.readFile('invoices.json') || [],
                    settings: storage.readFile('settings.json') || {}
                },
                stats: storage.getStatistics()
            };

            const backupPath = `data/backups/backup-${timestamp}.json`;
            
            // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
            const fs = require('fs');
            const path = require('path');
            const backupDir = path.join(process.cwd(), 'data', 'backups');
            
            if (!fs.existsSync(backupDir)) {
                fs.mkdirSync(backupDir, { recursive: true });
            }

            // حفظ النسخة الاحتياطية
            fs.writeFileSync(path.join(process.cwd(), backupPath), JSON.stringify(backupData, null, 2));
            
            console.log(`تم إنشاء نسخة احتياطية: ${backupPath}`);
            
            // تنظيف النسخ القديمة (الاحتفاظ بآخر 30 نسخة)
            this.cleanOldBackups();
            
            return { success: true, path: backupPath };
        } catch (error) {
            console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
            return { success: false, error: error.message };
        }
    }

    cleanOldBackups() {
        try {
            const fs = require('fs');
            const path = require('path');
            const backupDir = path.join(process.cwd(), 'data', 'backups');
            
            if (!fs.existsSync(backupDir)) return;
            
            const files = fs.readdirSync(backupDir)
                .filter(file => file.startsWith('backup-') && file.endsWith('.json'))
                .map(file => ({
                    name: file,
                    path: path.join(backupDir, file),
                    time: fs.statSync(path.join(backupDir, file)).mtime
                }))
                .sort((a, b) => b.time - a.time);

            // حذف النسخ الزائدة عن 30
            if (files.length > 30) {
                const filesToDelete = files.slice(30);
                filesToDelete.forEach(file => {
                    fs.unlinkSync(file.path);
                    console.log(`تم حذف النسخة الاحتياطية القديمة: ${file.name}`);
                });
            }
        } catch (error) {
            console.error('خطأ في تنظيف النسخ الاحتياطية:', error);
        }
    }

    restoreBackup(backupPath) {
        try {
            const fs = require('fs');
            const path = require('path');
            const fullPath = path.join(process.cwd(), backupPath);
            
            if (!fs.existsSync(fullPath)) {
                return { success: false, error: 'ملف النسخة الاحتياطية غير موجود' };
            }

            const backupData = JSON.parse(fs.readFileSync(fullPath, 'utf8'));
            
            // استعادة البيانات
            Object.keys(backupData.data).forEach(filename => {
                storage.writeFile(`${filename}.json`, backupData.data[filename]);
            });

            console.log('تم استعادة النسخة الاحتياطية بنجاح');
            return { success: true };
        } catch (error) {
            console.error('خطأ في استعادة النسخة الاحتياطية:', error);
            return { success: false, error: error.message };
        }
    }

    getBackupsList() {
        try {
            const fs = require('fs');
            const path = require('path');
            const backupDir = path.join(process.cwd(), 'data', 'backups');
            
            if (!fs.existsSync(backupDir)) return [];
            
            return fs.readdirSync(backupDir)
                .filter(file => file.startsWith('backup-') && file.endsWith('.json'))
                .map(file => {
                    const filePath = path.join(backupDir, file);
                    const stats = fs.statSync(filePath);
                    return {
                        name: file,
                        path: `data/backups/${file}`,
                        size: (stats.size / 1024).toFixed(2) + ' KB',
                        date: stats.mtime.toLocaleDateString('ar-EG'),
                        time: stats.mtime.toLocaleTimeString('ar-EG')
                    };
                })
                .sort((a, b) => new Date(b.date + ' ' + b.time) - new Date(a.date + ' ' + a.time));
        } catch (error) {
            console.error('خطأ في قراءة قائمة النسخ الاحتياطية:', error);
            return [];
        }
    }
}

// إنشاء مثيل من مدير النسخ الاحتياطي
const backupManager = new BackupManager();

// دوال مساعدة
function createManualBackup() {
    const result = backupManager.createBackup('manual');
    if (result.success) {
        alert('تم إنشاء نسخة احتياطية بنجاح');
    } else {
        alert('فشل في إنشاء النسخة الاحتياطية: ' + result.error);
    }
}

function showBackupsList() {
    const backups = backupManager.getBackupsList();
    console.log('النسخ الاحتياطية المتاحة:', backups);
    // يمكن إضافة واجهة لعرض القائمة
}
