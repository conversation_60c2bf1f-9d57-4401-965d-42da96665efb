// نظام اختصارات لوحة المفاتيح
class KeyboardShortcuts {
    constructor() {
        this.shortcuts = {
            // اختصارات عامة
            'ctrl+f': () => this.openGlobalSearch(),
            'ctrl+n': () => this.createNew(),
            'ctrl+s': () => this.save(),
            'ctrl+p': () => this.print(),
            'escape': () => this.closeModals(),
            'f1': () => this.showHelp(),
            
            // اختصارات التنقل
            'ctrl+1': () => app.loadPage('dashboard'),
            'ctrl+2': () => app.loadPage('products'),
            'ctrl+3': () => app.loadPage('customers'),
            'ctrl+4': () => app.loadPage('invoices'),
            'ctrl+5': () => app.loadPage('reports'),
            'ctrl+6': () => app.loadPage('activity'),
            'ctrl+7': () => app.loadPage('settings'),
            
            // اختصارات الفواتير
            'ctrl+shift+n': () => this.createNewInvoice(),
            'ctrl+shift+p': () => this.printLastInvoice(),
            
            // اختصارات المنتجات
            'ctrl+shift+m': () => this.addNewProduct(),
            
            // اختصارات العملاء
            'ctrl+shift+c': () => this.addNewCustomer(),
            
            // اختصارات أخرى
            'ctrl+b': () => this.createBackup(),
            'ctrl+shift+s': () => this.showStatistics(),
            'alt+f4': () => this.confirmExit()
        };
        
        this.init();
    }

    init() {
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        this.showShortcutsOnStartup();
    }

    handleKeyDown(e) {
        // تجاهل الاختصارات إذا كان المستخدم يكتب في حقل إدخال
        if (this.isInputFocused()) return;
        
        const key = this.getKeyString(e);
        
        if (this.shortcuts[key]) {
            e.preventDefault();
            this.shortcuts[key]();
            
            // تسجيل استخدام الاختصار
            if (typeof activityLogger !== 'undefined') {
                activityLogger.log('استخدام اختصار لوحة المفاتيح', { shortcut: key }, 'info');
            }
        }
    }

    getKeyString(e) {
        let key = '';
        
        if (e.ctrlKey) key += 'ctrl+';
        if (e.shiftKey) key += 'shift+';
        if (e.altKey) key += 'alt+';
        
        // مفاتيح خاصة
        if (e.key === 'Escape') key += 'escape';
        else if (e.key === 'F1') key += 'f1';
        else if (e.key === 'F4') key += 'f4';
        else key += e.key.toLowerCase();
        
        return key;
    }

    isInputFocused() {
        const activeElement = document.activeElement;
        return activeElement && (
            activeElement.tagName === 'INPUT' ||
            activeElement.tagName === 'TEXTAREA' ||
            activeElement.tagName === 'SELECT' ||
            activeElement.contentEditable === 'true'
        );
    }

    // تنفيذ الاختصارات
    openGlobalSearch() {
        if (typeof advancedSearch !== 'undefined') {
            advancedSearch.showGlobalSearchModal();
        }
    }

    createNew() {
        const currentPage = app.getCurrentPage();
        switch (currentPage) {
            case 'products':
                this.addNewProduct();
                break;
            case 'customers':
                this.addNewCustomer();
                break;
            case 'invoices':
                this.createNewInvoice();
                break;
            default:
                this.createNewInvoice();
        }
    }

    save() {
        // البحث عن نموذج مفتوح وحفظه
        const openModal = document.querySelector('.modal[style*="flex"]');
        if (openModal) {
            const saveButton = openModal.querySelector('button[type="submit"], .btn-primary');
            if (saveButton) {
                saveButton.click();
            }
        }
    }

    print() {
        const currentPage = app.getCurrentPage();
        if (currentPage === 'invoices') {
            this.printLastInvoice();
        } else {
            window.print();
        }
    }

    closeModals() {
        // إغلاق جميع النوافذ المفتوحة
        const modals = document.querySelectorAll('.modal[style*="flex"]');
        modals.forEach(modal => {
            const closeButton = modal.querySelector('.close-btn');
            if (closeButton) {
                closeButton.click();
            } else {
                modal.style.display = 'none';
            }
        });
    }

    showHelp() {
        this.showShortcutsModal();
    }

    createNewInvoice() {
        if (app.getCurrentPage() !== 'invoices') {
            app.loadPage('invoices');
        }
        setTimeout(() => {
            if (typeof showNewInvoiceModal === 'function') {
                showNewInvoiceModal();
            }
        }, 100);
    }

    addNewProduct() {
        if (app.getCurrentPage() !== 'products') {
            app.loadPage('products');
        }
        setTimeout(() => {
            if (typeof showAddProductModal === 'function') {
                showAddProductModal();
            }
        }, 100);
    }

    addNewCustomer() {
        if (app.getCurrentPage() !== 'customers') {
            app.loadPage('customers');
        }
        setTimeout(() => {
            if (typeof showAddCustomerModal === 'function') {
                showAddCustomerModal();
            }
        }, 100);
    }

    printLastInvoice() {
        const invoices = storage.readFile('invoices.json') || [];
        if (invoices.length > 0) {
            const lastInvoice = invoices[0]; // أحدث فاتورة
            if (typeof printInvoice === 'function') {
                printInvoice(lastInvoice.id);
            }
        } else {
            if (typeof appUtils !== 'undefined') {
                appUtils.showInfoMessage('لا توجد فواتير للطباعة');
            }
        }
    }

    createBackup() {
        if (typeof createManualBackup === 'function') {
            createManualBackup();
        }
    }

    showStatistics() {
        app.loadPage('reports');
    }

    confirmExit() {
        if (confirm('هل تريد إغلاق التطبيق؟')) {
            if (typeof window.close === 'function') {
                window.close();
            }
        }
    }

    // عرض نافذة الاختصارات
    showShortcutsModal() {
        const modalHTML = `
            <div id="shortcutsModal" class="modal">
                <div class="modal-content shortcuts-content">
                    <div class="modal-header">
                        <h2><i class="fas fa-keyboard"></i> اختصارات لوحة المفاتيح</h2>
                        <button class="close-btn" onclick="closeShortcutsModal()">&times;</button>
                    </div>
                    
                    <div class="shortcuts-body">
                        <div class="shortcuts-section">
                            <h3><i class="fas fa-globe"></i> اختصارات عامة</h3>
                            <div class="shortcuts-list">
                                <div class="shortcut-item">
                                    <kbd>Ctrl + F</kbd>
                                    <span>البحث الشامل</span>
                                </div>
                                <div class="shortcut-item">
                                    <kbd>Ctrl + N</kbd>
                                    <span>إنشاء جديد</span>
                                </div>
                                <div class="shortcut-item">
                                    <kbd>Ctrl + S</kbd>
                                    <span>حفظ</span>
                                </div>
                                <div class="shortcut-item">
                                    <kbd>Ctrl + P</kbd>
                                    <span>طباعة</span>
                                </div>
                                <div class="shortcut-item">
                                    <kbd>Esc</kbd>
                                    <span>إغلاق النوافذ</span>
                                </div>
                                <div class="shortcut-item">
                                    <kbd>F1</kbd>
                                    <span>المساعدة</span>
                                </div>
                            </div>
                        </div>

                        <div class="shortcuts-section">
                            <h3><i class="fas fa-compass"></i> التنقل</h3>
                            <div class="shortcuts-list">
                                <div class="shortcut-item">
                                    <kbd>Ctrl + 1</kbd>
                                    <span>لوحة التحكم</span>
                                </div>
                                <div class="shortcut-item">
                                    <kbd>Ctrl + 2</kbd>
                                    <span>المنتجات</span>
                                </div>
                                <div class="shortcut-item">
                                    <kbd>Ctrl + 3</kbd>
                                    <span>العملاء</span>
                                </div>
                                <div class="shortcut-item">
                                    <kbd>Ctrl + 4</kbd>
                                    <span>الفواتير</span>
                                </div>
                                <div class="shortcut-item">
                                    <kbd>Ctrl + 5</kbd>
                                    <span>التقارير</span>
                                </div>
                                <div class="shortcut-item">
                                    <kbd>Ctrl + 6</kbd>
                                    <span>سجل الأنشطة</span>
                                </div>
                            </div>
                        </div>

                        <div class="shortcuts-section">
                            <h3><i class="fas fa-bolt"></i> إجراءات سريعة</h3>
                            <div class="shortcuts-list">
                                <div class="shortcut-item">
                                    <kbd>Ctrl + Shift + N</kbd>
                                    <span>فاتورة جديدة</span>
                                </div>
                                <div class="shortcut-item">
                                    <kbd>Ctrl + Shift + M</kbd>
                                    <span>منتج جديد</span>
                                </div>
                                <div class="shortcut-item">
                                    <kbd>Ctrl + Shift + C</kbd>
                                    <span>عميل جديد</span>
                                </div>
                                <div class="shortcut-item">
                                    <kbd>Ctrl + B</kbd>
                                    <span>نسخة احتياطية</span>
                                </div>
                                <div class="shortcut-item">
                                    <kbd>Ctrl + Shift + P</kbd>
                                    <span>طباعة آخر فاتورة</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="shortcuts-footer">
                        <button class="btn btn-primary" onclick="closeShortcutsModal()">
                            <i class="fas fa-check"></i> فهمت
                        </button>
                    </div>
                </div>
            </div>
        `;

        // إزالة النافذة السابقة إن وجدت
        const existingModal = document.getElementById('shortcutsModal');
        if (existingModal) {
            existingModal.remove();
        }

        // إضافة النافذة الجديدة
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        
        // إضافة الأنماط
        this.addShortcutsStyles();
        
        // إظهار النافذة
        document.getElementById('shortcutsModal').style.display = 'flex';
    }

    // عرض الاختصارات عند بدء التشغيل
    showShortcutsOnStartup() {
        // عرض تلميح سريع عن الاختصارات
        setTimeout(() => {
            if (typeof appUtils !== 'undefined') {
                appUtils.showInfoMessage('اضغط F1 لعرض اختصارات لوحة المفاتيح', 5000);
            }
        }, 3000);
    }

    // إضافة أنماط الاختصارات
    addShortcutsStyles() {
        if (!document.getElementById('shortcutsStyles')) {
            const style = document.createElement('style');
            style.id = 'shortcutsStyles';
            style.textContent = `
                .shortcuts-content { max-width: 800px; max-height: 90vh; overflow-y: auto; }
                .shortcuts-body { padding: 25px; }
                .shortcuts-section { margin-bottom: 30px; }
                .shortcuts-section h3 { color: #333; margin-bottom: 15px; display: flex; align-items: center; gap: 10px; font-size: 1.1rem; }
                .shortcuts-list { display: grid; gap: 10px; }
                .shortcut-item { display: flex; justify-content: space-between; align-items: center; padding: 10px 15px; background: #f8f9fa; border-radius: 8px; }
                .shortcut-item kbd { background: #667eea; color: white; padding: 4px 8px; border-radius: 4px; font-size: 0.9rem; font-weight: 600; }
                .shortcut-item span { color: #333; font-weight: 500; }
                .shortcuts-footer { padding: 20px 25px; border-top: 1px solid #eee; text-align: center; }
            `;
            document.head.appendChild(style);
        }
    }
}

// إنشاء مثيل من نظام الاختصارات
const keyboardShortcuts = new KeyboardShortcuts();

// دوال مساعدة
function closeShortcutsModal() {
    const modal = document.getElementById('shortcutsModal');
    if (modal) {
        modal.remove();
    }
}
