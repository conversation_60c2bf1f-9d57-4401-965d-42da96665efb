@echo off
chcp 65001 >nul
title بناء تطبيق مصطفي كشاف للمفروشات
color 0A

echo ========================================
echo 🏠 بناء تطبيق مصطفي كشاف للمفروشات
echo ========================================
echo.

echo 🧹 تنظيف الملفات القديمة...
taskkill /f /im electron.exe 2>nul
if exist dist (
    echo حذف مجلد dist القديم...
    rmdir /s /q dist
)
if exist node_modules\.cache (
    echo حذف ملفات التخزين المؤقت...
    rmdir /s /q node_modules\.cache
)

echo.
echo 📦 التحقق من التبعيات...
call npm install

echo.
echo 🔨 بناء التطبيق...
echo هذا قد يستغرق بضع دقائق...
call npm run dist

echo.
if exist dist (
    echo ✅ تم الانتهاء من البناء بنجاح!
    echo 📁 ملفات التثبيت موجودة في مجلد dist:
    dir dist\*.exe /b
    echo.
    echo 🚀 يمكنك الآن توزيع ملف Setup للمستخدمين
) else (
    echo ❌ فشل في البناء! تحقق من الأخطاء أعلاه
)

echo.
pause
