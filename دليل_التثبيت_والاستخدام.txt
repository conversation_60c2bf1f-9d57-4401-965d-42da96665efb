🏠 برنامج مصطفي كشاف للمفروشات - نظام إدارة المبيعات
=====================================================

📋 دليل التثبيت والاستخدام الشامل
==================================

🔧 متطلبات النظام:
==================
✅ نظام التشغيل: Windows 7 32-bit أو أحدث
✅ المعالج: Intel Pentium 4 أو أحدث  
✅ الذاكرة: 2 GB RAM كحد أدنى
✅ مساحة القرص: 500 MB مساحة فارغة
✅ الشاشة: دقة 1024x768 كحد أدنى

📦 خطوات التثبيت:
==================

الطريقة الأولى - التثبيت الكامل:
--------------------------------
1. قم بتشغيل ملف "مصطفي-كشاف-للمفروشات-Setup.exe"
2. اتبع خطوات المعالج:
   - اختر مجلد التثبيت (افتراضي: C:\Program Files\مصطفي كشاف للمفروشات)
   - اختر إنشاء اختصار على سطح المكتب
   - اختر إنشاء اختصار في قائمة ابدأ
3. انقر "تثبيت" وانتظر انتهاء العملية
4. انقر "إنهاء" لإكمال التثبيت

الطريقة الثانية - النسخة المحمولة:
----------------------------------
1. قم بتشغيل ملف "مصطفي-كشاف-للمفروشات-Portable.exe"
2. البرنامج سيعمل مباشرة بدون تثبيت
3. يمكن نسخه على فلاشة USB واستخدامه على أي جهاز

🚀 تشغيل البرنامج لأول مرة:
=============================
1. انقر نقراً مزدوجاً على أيقونة البرنامج
2. انتظر تحميل البرنامج (قد يستغرق 10-15 ثانية في المرة الأولى)
3. ستظهر شاشة تسجيل الدخول

🔐 تسجيل الدخول الأول:
======================
👤 اسم المستخدم: admin
🔑 كلمة المرور: 123456

⚠️ مهم: قم بتغيير كلمة المرور بعد تسجيل الدخول الأول!

📚 كيفية الاستخدام:
===================

🏠 لوحة التحكم:
---------------
- عرض إحصائيات سريعة للمبيعات والأرباح
- تنبيهات المخزون المنخفض
- آخر الفواتير المنشأة
- إجراءات سريعة

📦 إدارة المنتجات:
------------------
1. انقر على "المنتجات" في القائمة الجانبية
2. لإضافة منتج جديد: انقر "إضافة منتج جديد"
3. املأ البيانات المطلوبة:
   - اسم المنتج
   - الفئة (كنب، طاولات، كراسي، إلخ)
   - السعر
   - تكلفة الشراء (اختياري)
   - الكمية في المخزون
   - وصف المنتج (اختياري)
4. انقر "حفظ"

👥 إدارة العملاء:
-----------------
1. انقر على "العملاء" في القائمة الجانبية
2. لإضافة عميل جديد: انقر "إضافة عميل جديد"
3. املأ البيانات:
   - اسم العميل
   - رقم الهاتف
   - العنوان (اختياري)
   - البريد الإلكتروني (اختياري)
4. انقر "حفظ"

🧾 إنشاء الفواتير:
==================
1. انقر على "الفواتير" في القائمة الجانبية
2. انقر "فاتورة جديدة"
3. اختر العميل أو أدخل بياناته
4. أضف المنتجات:
   - انقر على بطاقة المنتج المطلوب
   - عدل الكمية حسب الحاجة
   - كرر العملية لإضافة منتجات أخرى
5. أضف خصم إذا لزم الأمر
6. اختر طريقة الدفع
7. أضف ملاحظات (اختياري)
8. انقر "حفظ وطباعة" أو "حفظ الفاتورة"

🖨️ طباعة الفواتير:
===================
- الفواتير محسنة للطباعة على ورق A4
- تأكد من إعداد الطابعة على ورق A4
- اختر "طباعة ملونة" للحصول على أفضل نتيجة
- يمكن طباعة الفاتورة من قائمة الفواتير بالنقر على أيقونة الطابعة

⌨️ اختصارات لوحة المفاتيح:
============================
🔍 Ctrl + F: البحث الشامل
📄 Ctrl + N: إنشاء جديد
💾 Ctrl + S: حفظ
🖨️ Ctrl + P: طباعة
❌ Esc: إغلاق النوافذ
❓ F1: المساعدة

🧭 التنقل:
Ctrl + 1: لوحة التحكم
Ctrl + 2: المنتجات  
Ctrl + 3: العملاء
Ctrl + 4: الفواتير
Ctrl + 5: التقارير
Ctrl + 6: سجل الأنشطة

⚡ إجراءات سريعة:
Ctrl + Shift + N: فاتورة جديدة
Ctrl + Shift + M: منتج جديد
Ctrl + Shift + C: عميل جديد
Ctrl + B: نسخة احتياطية

📊 التقارير والإحصائيات:
=========================
- انقر على "التقارير" لعرض:
  - رسوم بيانية للمبيعات
  - إحصائيات الأرباح
  - تحليل المخزون
  - المبيعات الشهرية
- يمكن تصدير التقارير بصيغة JSON

🔍 البحث المتقدم:
==================
- اضغط Ctrl + F أو انقر على أيقونة البحث
- ابحث في جميع البيانات (منتجات، عملاء، فواتير)
- عرض النتائج مصنفة حسب النوع
- حفظ تاريخ البحث

💾 النسخ الاحتياطية:
====================
- يتم إنشاء نسخة احتياطية تلقائياً كل ساعة
- النسخ محفوظة في مجلد "data/backups"
- يمكن إنشاء نسخة احتياطية يدوية من سجل الأنشطة
- يتم الاحتفاظ بآخر 30 نسخة احتياطية

📝 سجل الأنشطة:
================
- تسجيل جميع العمليات (إضافة، تعديل، حذف، طباعة)
- تتبع المستخدم والوقت لكل عملية
- إمكانية تصدير السجل للمراجعة
- تصفية الأنشطة حسب النوع

⚠️ نصائح مهمة:
===============
✅ قم بعمل نسخة احتياطية من مجلد "data" بانتظام
✅ لا تحذف ملفات البرنامج الأساسية
✅ تأكد من إغلاق البرنامج بشكل صحيح لحفظ البيانات
✅ استخدم كلمات مرور قوية للمستخدمين
✅ راجع تنبيهات المخزون بانتظام

🔧 حل المشاكل الشائعة:
=======================

المشكلة: البرنامج لا يبدأ
الحل: 
- تأكد من تثبيت البرنامج بشكل صحيح
- شغل البرنامج كمدير (Run as Administrator)
- تأكد من توفر مساحة كافية على القرص

المشكلة: فقدان البيانات
الحل:
- ابحث عن النسخ الاحتياطية في مجلد "data/backups"
- استعد آخر نسخة احتياطية متاحة

المشكلة: مشاكل في الطباعة
الحل:
- تأكد من إعداد الطابعة على ورق A4
- تحقق من تعريفات الطابعة
- جرب الطباعة من برنامج آخر للتأكد من عمل الطابعة

📞 الدعم الفني:
===============
للمساعدة والدعم الفني:
📧 البريد الإلكتروني: <EMAIL>
📱 الهاتف: 02-12345678
🕐 ساعات العمل: من 9 صباحاً إلى 6 مساءً

💻 تم التطوير والتصميم بواسطة:
==============================
👨‍💻 عمرو السيد اليماني
🚀 نظام إدارة مبيعات متطور ومتميز

© 2024 جميع الحقوق محفوظة لمعرض مصطفي كشاف للمفروشات
