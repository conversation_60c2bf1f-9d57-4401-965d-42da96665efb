// نظام البحث المتقدم
class AdvancedSearch {
    constructor() {
        this.searchHistory = [];
        this.maxHistoryItems = 20;
        this.init();
    }

    init() {
        this.loadSearchHistory();
        this.setupGlobalSearch();
    }

    // إعداد البحث العام
    setupGlobalSearch() {
        // إضافة اختصار Ctrl+F للبحث
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                this.showGlobalSearchModal();
            }
        });
    }

    // البحث الشامل في جميع البيانات
    globalSearch(query) {
        if (!query || query.length < 2) return { results: [], total: 0 };

        const results = {
            products: this.searchInProducts(query),
            customers: this.searchInCustomers(query),
            invoices: this.searchInInvoices(query)
        };

        const totalResults = results.products.length + results.customers.length + results.invoices.length;

        // حفظ في تاريخ البحث
        this.addToSearchHistory(query, totalResults);

        return {
            results: results,
            total: totalResults,
            query: query
        };
    }

    // البحث في المنتجات
    searchInProducts(query) {
        const products = storage.readFile('products.json') || [];
        return products.filter(product => 
            this.matchesQuery(product.name, query) ||
            this.matchesQuery(product.category, query) ||
            this.matchesQuery(product.description, query) ||
            product.price.toString().includes(query)
        ).map(product => ({
            ...product,
            type: 'product',
            title: product.name,
            subtitle: `${product.category} - ${product.price} جنيه`,
            icon: 'fa-box'
        }));
    }

    // البحث في العملاء
    searchInCustomers(query) {
        const customers = storage.readFile('customers.json') || [];
        return customers.filter(customer => 
            this.matchesQuery(customer.name, query) ||
            this.matchesQuery(customer.phone, query) ||
            this.matchesQuery(customer.address, query) ||
            this.matchesQuery(customer.email, query)
        ).map(customer => ({
            ...customer,
            type: 'customer',
            title: customer.name,
            subtitle: customer.phone,
            icon: 'fa-user'
        }));
    }

    // البحث في الفواتير
    searchInInvoices(query) {
        const invoices = storage.readFile('invoices.json') || [];
        return invoices.filter(invoice => 
            this.matchesQuery(invoice.invoiceNumber, query) ||
            this.matchesQuery(invoice.customerName, query) ||
            this.matchesQuery(invoice.customerPhone, query) ||
            invoice.total.toString().includes(query)
        ).map(invoice => ({
            ...invoice,
            type: 'invoice',
            title: invoice.invoiceNumber,
            subtitle: `${invoice.customerName} - ${invoice.total} جنيه`,
            icon: 'fa-file-invoice'
        }));
    }

    // فحص تطابق النص
    matchesQuery(text, query) {
        if (!text) return false;
        return text.toString().toLowerCase().includes(query.toLowerCase());
    }

    // إضافة إلى تاريخ البحث
    addToSearchHistory(query, resultsCount) {
        const historyItem = {
            query: query,
            resultsCount: resultsCount,
            timestamp: new Date().toISOString(),
            date: new Date().toLocaleDateString('ar-EG'),
            time: new Date().toLocaleTimeString('ar-EG')
        };

        // إزالة البحث المكرر إن وجد
        this.searchHistory = this.searchHistory.filter(item => item.query !== query);
        
        // إضافة في المقدمة
        this.searchHistory.unshift(historyItem);
        
        // الاحتفاظ بآخر 20 بحث فقط
        if (this.searchHistory.length > this.maxHistoryItems) {
            this.searchHistory = this.searchHistory.slice(0, this.maxHistoryItems);
        }

        this.saveSearchHistory();
    }

    // حفظ تاريخ البحث
    saveSearchHistory() {
        localStorage.setItem('searchHistory', JSON.stringify(this.searchHistory));
    }

    // تحميل تاريخ البحث
    loadSearchHistory() {
        try {
            const saved = localStorage.getItem('searchHistory');
            this.searchHistory = saved ? JSON.parse(saved) : [];
        } catch (error) {
            console.error('خطأ في تحميل تاريخ البحث:', error);
            this.searchHistory = [];
        }
    }

    // إظهار نافذة البحث العام
    showGlobalSearchModal() {
        const modalHTML = `
            <div id="globalSearchModal" class="modal search-modal">
                <div class="modal-content search-content">
                    <div class="search-header">
                        <h2><i class="fas fa-search"></i> البحث الشامل</h2>
                        <button class="close-btn" onclick="closeGlobalSearchModal()">&times;</button>
                    </div>
                    
                    <div class="search-input-section">
                        <div class="search-input-group">
                            <i class="fas fa-search"></i>
                            <input type="text" id="globalSearchInput" placeholder="ابحث في المنتجات، العملاء، والفواتير..." 
                                   onkeyup="performGlobalSearch()" autofocus>
                            <button class="clear-search" onclick="clearGlobalSearch()" style="display: none;">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>

                    <div class="search-results" id="globalSearchResults">
                        <div class="search-placeholder">
                            <i class="fas fa-search"></i>
                            <p>ابدأ بكتابة كلمة البحث...</p>
                        </div>
                    </div>

                    <div class="search-history" id="searchHistorySection">
                        <h3><i class="fas fa-history"></i> عمليات البحث الأخيرة</h3>
                        <div class="history-items" id="searchHistoryItems">
                            ${this.renderSearchHistory()}
                        </div>
                    </div>
                </div>
            </div>
        `;

        // إزالة النافذة السابقة إن وجدت
        const existingModal = document.getElementById('globalSearchModal');
        if (existingModal) {
            existingModal.remove();
        }

        // إضافة النافذة الجديدة
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        
        // إضافة الأنماط
        this.addSearchStyles();
        
        // إظهار النافذة
        document.getElementById('globalSearchModal').style.display = 'flex';
        
        // التركيز على حقل البحث
        setTimeout(() => {
            document.getElementById('globalSearchInput').focus();
        }, 100);
    }

    // عرض تاريخ البحث
    renderSearchHistory() {
        if (this.searchHistory.length === 0) {
            return '<div class="no-history">لا يوجد تاريخ بحث</div>';
        }

        return this.searchHistory.map(item => `
            <div class="history-item" onclick="searchFromHistory('${item.query}')">
                <div class="history-query">${item.query}</div>
                <div class="history-meta">
                    <span class="results-count">${item.resultsCount} نتيجة</span>
                    <span class="search-time">${item.date}</span>
                </div>
            </div>
        `).join('');
    }

    // عرض نتائج البحث
    renderSearchResults(searchData) {
        if (searchData.total === 0) {
            return `
                <div class="no-results">
                    <i class="fas fa-search-minus"></i>
                    <p>لم يتم العثور على نتائج لـ "${searchData.query}"</p>
                </div>
            `;
        }

        let html = `<div class="results-summary">تم العثور على ${searchData.total} نتيجة</div>`;

        // نتائج المنتجات
        if (searchData.results.products.length > 0) {
            html += `
                <div class="results-section">
                    <h4><i class="fas fa-box"></i> المنتجات (${searchData.results.products.length})</h4>
                    <div class="results-list">
                        ${searchData.results.products.map(item => this.renderResultItem(item)).join('')}
                    </div>
                </div>
            `;
        }

        // نتائج العملاء
        if (searchData.results.customers.length > 0) {
            html += `
                <div class="results-section">
                    <h4><i class="fas fa-users"></i> العملاء (${searchData.results.customers.length})</h4>
                    <div class="results-list">
                        ${searchData.results.customers.map(item => this.renderResultItem(item)).join('')}
                    </div>
                </div>
            `;
        }

        // نتائج الفواتير
        if (searchData.results.invoices.length > 0) {
            html += `
                <div class="results-section">
                    <h4><i class="fas fa-file-invoice"></i> الفواتير (${searchData.results.invoices.length})</h4>
                    <div class="results-list">
                        ${searchData.results.invoices.map(item => this.renderResultItem(item)).join('')}
                    </div>
                </div>
            `;
        }

        return html;
    }

    // عرض عنصر نتيجة
    renderResultItem(item) {
        return `
            <div class="result-item" onclick="openSearchResult('${item.type}', '${item.id}')">
                <div class="result-icon">
                    <i class="fas ${item.icon}"></i>
                </div>
                <div class="result-content">
                    <div class="result-title">${item.title}</div>
                    <div class="result-subtitle">${item.subtitle}</div>
                </div>
                <div class="result-action">
                    <i class="fas fa-chevron-left"></i>
                </div>
            </div>
        `;
    }

    // إضافة أنماط البحث
    addSearchStyles() {
        if (!document.getElementById('searchStyles')) {
            const style = document.createElement('style');
            style.id = 'searchStyles';
            style.textContent = `
                .search-modal .modal-content { max-width: 800px; max-height: 90vh; overflow: hidden; display: flex; flex-direction: column; }
                .search-header { padding: 20px 25px; border-bottom: 1px solid #eee; }
                .search-input-section { padding: 20px 25px; border-bottom: 1px solid #eee; }
                .search-input-group { position: relative; }
                .search-input-group i { position: absolute; right: 15px; top: 50%; transform: translateY(-50%); color: #667eea; }
                .search-input-group input { width: 100%; padding: 15px 45px 15px 15px; border: 2px solid #e1e5e9; border-radius: 10px; font-size: 1rem; }
                .search-input-group input:focus { border-color: #667eea; outline: none; }
                .clear-search { position: absolute; left: 15px; top: 50%; transform: translateY(-50%); background: none; border: none; color: #999; cursor: pointer; }
                .search-results { flex: 1; overflow-y: auto; padding: 20px 25px; }
                .search-placeholder, .no-results { text-align: center; padding: 40px; color: #666; }
                .search-placeholder i, .no-results i { font-size: 3rem; margin-bottom: 15px; color: #cbd5e0; }
                .results-summary { background: #f0f8ff; padding: 10px 15px; border-radius: 8px; margin-bottom: 20px; color: #2c5282; font-weight: 600; }
                .results-section { margin-bottom: 25px; }
                .results-section h4 { color: #333; margin-bottom: 15px; display: flex; align-items: center; gap: 10px; }
                .result-item { display: flex; align-items: center; gap: 15px; padding: 12px; border-radius: 8px; cursor: pointer; transition: background 0.3s ease; }
                .result-item:hover { background: #f8f9fa; }
                .result-icon { width: 40px; height: 40px; background: #667eea; color: white; border-radius: 8px; display: flex; align-items: center; justify-content: center; }
                .result-content { flex: 1; }
                .result-title { font-weight: 600; color: #333; margin-bottom: 4px; }
                .result-subtitle { font-size: 0.9rem; color: #666; }
                .result-action { color: #999; }
                .search-history { padding: 20px 25px; border-top: 1px solid #eee; }
                .search-history h3 { margin-bottom: 15px; color: #333; display: flex; align-items: center; gap: 10px; }
                .history-item { display: flex; justify-content: space-between; align-items: center; padding: 10px; border-radius: 6px; cursor: pointer; transition: background 0.3s ease; }
                .history-item:hover { background: #f8f9fa; }
                .history-query { font-weight: 600; color: #333; }
                .history-meta { display: flex; gap: 15px; font-size: 0.8rem; color: #666; }
                .no-history { text-align: center; color: #999; padding: 20px; }
            `;
            document.head.appendChild(style);
        }
    }
}

// إنشاء مثيل من البحث المتقدم
const advancedSearch = new AdvancedSearch();

// دوال مساعدة
function performGlobalSearch() {
    const query = document.getElementById('globalSearchInput').value.trim();
    const clearBtn = document.querySelector('.clear-search');
    const resultsContainer = document.getElementById('globalSearchResults');
    const historySection = document.getElementById('searchHistorySection');

    if (query.length === 0) {
        clearBtn.style.display = 'none';
        resultsContainer.innerHTML = `
            <div class="search-placeholder">
                <i class="fas fa-search"></i>
                <p>ابدأ بكتابة كلمة البحث...</p>
            </div>
        `;
        historySection.style.display = 'block';
        return;
    }

    clearBtn.style.display = 'block';
    historySection.style.display = 'none';

    if (query.length < 2) {
        resultsContainer.innerHTML = `
            <div class="search-placeholder">
                <i class="fas fa-search"></i>
                <p>اكتب حرفين على الأقل للبحث...</p>
            </div>
        `;
        return;
    }

    const searchData = advancedSearch.globalSearch(query);
    resultsContainer.innerHTML = advancedSearch.renderSearchResults(searchData);
}

function clearGlobalSearch() {
    document.getElementById('globalSearchInput').value = '';
    performGlobalSearch();
    document.getElementById('globalSearchInput').focus();
}

function searchFromHistory(query) {
    document.getElementById('globalSearchInput').value = query;
    performGlobalSearch();
}

function openSearchResult(type, id) {
    closeGlobalSearchModal();
    
    switch (type) {
        case 'product':
            app.loadPage('products');
            // يمكن إضافة تمييز للمنتج المحدد
            break;
        case 'customer':
            app.loadPage('customers');
            // يمكن إضافة تمييز للعميل المحدد
            break;
        case 'invoice':
            app.loadPage('invoices');
            // يمكن إضافة تمييز للفاتورة المحددة
            break;
    }
}

function closeGlobalSearchModal() {
    const modal = document.getElementById('globalSearchModal');
    if (modal) {
        modal.remove();
    }
}
