// نظام الرسوم البيانية والإحصائيات
class ChartsManager {
    constructor() {
        this.colors = {
            primary: '#667eea',
            secondary: '#764ba2',
            success: '#38a169',
            warning: '#d69e2e',
            danger: '#e53e3e',
            info: '#3182ce'
        };
    }

    // إنشاء رسم بياني دائري للمبيعات حسب الفئة
    createCategoryPieChart(containerId) {
        const products = storage.readFile('products.json') || [];
        const invoices = storage.readFile('invoices.json') || [];
        
        // حساب المبيعات حسب الفئة
        const categoryStats = {};
        
        invoices.forEach(invoice => {
            invoice.items.forEach(item => {
                const product = products.find(p => p.id === item.productId);
                if (product) {
                    const category = product.category;
                    categoryStats[category] = (categoryStats[category] || 0) + item.total;
                }
            });
        });

        const container = document.getElementById(containerId);
        if (!container) return;

        const data = Object.entries(categoryStats);
        if (data.length === 0) {
            container.innerHTML = '<div class="no-chart-data">لا توجد بيانات للعرض</div>';
            return;
        }

        const total = data.reduce((sum, [, value]) => sum + value, 0);
        
        let html = '<div class="pie-chart">';
        let currentAngle = 0;
        
        data.forEach(([category, value], index) => {
            const percentage = (value / total) * 100;
            const angle = (value / total) * 360;
            const color = this.getColorByIndex(index);
            
            html += `
                <div class="pie-slice" style="
                    --start-angle: ${currentAngle}deg;
                    --end-angle: ${currentAngle + angle}deg;
                    --color: ${color};
                "></div>
            `;
            
            currentAngle += angle;
        });
        
        html += '</div>';
        
        // إضافة وسيلة الإيضاح
        html += '<div class="chart-legend">';
        data.forEach(([category, value], index) => {
            const percentage = ((value / total) * 100).toFixed(1);
            const color = this.getColorByIndex(index);
            
            html += `
                <div class="legend-item">
                    <div class="legend-color" style="background: ${color}"></div>
                    <div class="legend-text">
                        <span class="legend-label">${category}</span>
                        <span class="legend-value">${value.toLocaleString()} جنيه (${percentage}%)</span>
                    </div>
                </div>
            `;
        });
        html += '</div>';
        
        container.innerHTML = html;
    }

    // إنشاء رسم بياني شريطي للمبيعات الشهرية
    createMonthlySalesChart(containerId) {
        const invoices = storage.readFile('invoices.json') || [];
        
        // حساب المبيعات الشهرية لآخر 6 أشهر
        const monthlyStats = {};
        const currentDate = new Date();
        
        // إنشاء آخر 6 أشهر
        for (let i = 5; i >= 0; i--) {
            const date = new Date(currentDate.getFullYear(), currentDate.getMonth() - i, 1);
            const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
            const monthName = date.toLocaleDateString('ar-EG', { month: 'long', year: 'numeric' });
            monthlyStats[monthKey] = { name: monthName, value: 0 };
        }
        
        // حساب المبيعات
        invoices.forEach(invoice => {
            const invoiceDate = new Date(invoice.createdAt);
            const monthKey = `${invoiceDate.getFullYear()}-${String(invoiceDate.getMonth() + 1).padStart(2, '0')}`;
            
            if (monthlyStats[monthKey]) {
                monthlyStats[monthKey].value += invoice.total;
            }
        });

        const container = document.getElementById(containerId);
        if (!container) return;

        const data = Object.values(monthlyStats);
        const maxValue = Math.max(...data.map(d => d.value));
        
        if (maxValue === 0) {
            container.innerHTML = '<div class="no-chart-data">لا توجد مبيعات للعرض</div>';
            return;
        }

        let html = '<div class="bar-chart">';
        
        data.forEach((item, index) => {
            const height = maxValue > 0 ? (item.value / maxValue) * 100 : 0;
            const color = this.getColorByIndex(index);
            
            html += `
                <div class="bar-item">
                    <div class="bar" style="height: ${height}%; background: ${color}">
                        <div class="bar-value">${item.value.toLocaleString()}</div>
                    </div>
                    <div class="bar-label">${item.name}</div>
                </div>
            `;
        });
        
        html += '</div>';
        container.innerHTML = html;
    }

    // إنشاء رسم بياني خطي للأرباح
    createProfitLineChart(containerId) {
        const invoices = storage.readFile('invoices.json') || [];
        
        // حساب الأرباح اليومية لآخر 30 يوم
        const dailyProfits = {};
        const currentDate = new Date();
        
        // إنشاء آخر 30 يوم
        for (let i = 29; i >= 0; i--) {
            const date = new Date(currentDate);
            date.setDate(date.getDate() - i);
            const dateKey = date.toISOString().split('T')[0];
            dailyProfits[dateKey] = 0;
        }
        
        // حساب الأرباح
        invoices.forEach(invoice => {
            const invoiceDate = new Date(invoice.createdAt).toISOString().split('T')[0];
            if (dailyProfits.hasOwnProperty(invoiceDate)) {
                dailyProfits[invoiceDate] += invoice.profit || 0;
            }
        });

        const container = document.getElementById(containerId);
        if (!container) return;

        const data = Object.entries(dailyProfits);
        const maxValue = Math.max(...data.map(([, value]) => value));
        
        if (maxValue === 0) {
            container.innerHTML = '<div class="no-chart-data">لا توجد أرباح للعرض</div>';
            return;
        }

        let html = '<div class="line-chart">';
        let pathData = '';
        
        data.forEach(([date, value], index) => {
            const x = (index / (data.length - 1)) * 100;
            const y = 100 - ((value / maxValue) * 80); // 80% للرسم، 20% للهوامش
            
            if (index === 0) {
                pathData += `M ${x} ${y}`;
            } else {
                pathData += ` L ${x} ${y}`;
            }
        });
        
        html += `
            <svg class="line-svg" viewBox="0 0 100 100">
                <path d="${pathData}" stroke="${this.colors.success}" stroke-width="2" fill="none"/>
                ${data.map(([date, value], index) => {
                    const x = (index / (data.length - 1)) * 100;
                    const y = 100 - ((value / maxValue) * 80);
                    return `<circle cx="${x}" cy="${y}" r="2" fill="${this.colors.success}"/>`;
                }).join('')}
            </svg>
        `;
        
        html += '</div>';
        
        // إضافة معلومات إضافية
        const totalProfit = data.reduce((sum, [, value]) => sum + value, 0);
        const avgProfit = totalProfit / data.length;
        
        html += `
            <div class="chart-info">
                <div class="info-item">
                    <span class="info-label">إجمالي الأرباح (30 يوم):</span>
                    <span class="info-value">${totalProfit.toLocaleString()} جنيه</span>
                </div>
                <div class="info-item">
                    <span class="info-label">متوسط الربح اليومي:</span>
                    <span class="info-value">${avgProfit.toLocaleString()} جنيه</span>
                </div>
            </div>
        `;
        
        container.innerHTML = html;
    }

    // إنشاء إحصائيات المخزون
    createInventoryStats(containerId) {
        const products = storage.readFile('products.json') || [];
        
        const container = document.getElementById(containerId);
        if (!container) return;

        if (products.length === 0) {
            container.innerHTML = '<div class="no-chart-data">لا توجد منتجات للعرض</div>';
            return;
        }

        // تصنيف المنتجات حسب حالة المخزون
        const stockStats = {
            outOfStock: products.filter(p => p.stock === 0).length,
            lowStock: products.filter(p => p.stock > 0 && p.stock <= 5).length,
            normalStock: products.filter(p => p.stock > 5 && p.stock <= 20).length,
            highStock: products.filter(p => p.stock > 20).length
        };

        const totalProducts = products.length;
        
        let html = '<div class="inventory-stats">';
        
        const stockCategories = [
            { key: 'outOfStock', label: 'نفد من المخزون', color: this.colors.danger, icon: 'fa-times-circle' },
            { key: 'lowStock', label: 'مخزون منخفض', color: this.colors.warning, icon: 'fa-exclamation-triangle' },
            { key: 'normalStock', label: 'مخزون عادي', color: this.colors.info, icon: 'fa-info-circle' },
            { key: 'highStock', label: 'مخزون عالي', color: this.colors.success, icon: 'fa-check-circle' }
        ];
        
        stockCategories.forEach(category => {
            const count = stockStats[category.key];
            const percentage = totalProducts > 0 ? ((count / totalProducts) * 100).toFixed(1) : 0;
            
            html += `
                <div class="inventory-item">
                    <div class="inventory-icon" style="color: ${category.color}">
                        <i class="fas ${category.icon}"></i>
                    </div>
                    <div class="inventory-content">
                        <div class="inventory-label">${category.label}</div>
                        <div class="inventory-value">${count} منتج (${percentage}%)</div>
                        <div class="inventory-bar">
                            <div class="inventory-progress" style="width: ${percentage}%; background: ${category.color}"></div>
                        </div>
                    </div>
                </div>
            `;
        });
        
        html += '</div>';
        container.innerHTML = html;
    }

    // الحصول على لون حسب الفهرس
    getColorByIndex(index) {
        const colorArray = Object.values(this.colors);
        return colorArray[index % colorArray.length];
    }

    // إضافة أنماط الرسوم البيانية
    addChartStyles() {
        if (!document.getElementById('chartStyles')) {
            const style = document.createElement('style');
            style.id = 'chartStyles';
            style.textContent = `
                .chart-container { background: white; padding: 20px; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.08); margin-bottom: 20px; }
                .chart-title { font-size: 1.2rem; font-weight: 600; color: #333; margin-bottom: 20px; display: flex; align-items: center; gap: 10px; }
                .no-chart-data { text-align: center; padding: 40px; color: #666; }
                
                /* رسم دائري */
                .pie-chart { width: 200px; height: 200px; border-radius: 50%; margin: 0 auto 20px; position: relative; background: conic-gradient(from 0deg, var(--colors)); }
                .chart-legend { display: grid; gap: 10px; }
                .legend-item { display: flex; align-items: center; gap: 10px; }
                .legend-color { width: 16px; height: 16px; border-radius: 3px; }
                .legend-text { flex: 1; }
                .legend-label { font-weight: 600; color: #333; }
                .legend-value { font-size: 0.9rem; color: #666; margin-right: 10px; }
                
                /* رسم شريطي */
                .bar-chart { display: flex; align-items: end; gap: 10px; height: 200px; padding: 20px 0; }
                .bar-item { flex: 1; display: flex; flex-direction: column; align-items: center; }
                .bar { width: 100%; position: relative; border-radius: 4px 4px 0 0; transition: all 0.3s ease; }
                .bar:hover { opacity: 0.8; }
                .bar-value { position: absolute; top: -25px; left: 50%; transform: translateX(-50%); font-size: 0.8rem; font-weight: 600; color: #333; }
                .bar-label { margin-top: 10px; font-size: 0.8rem; color: #666; text-align: center; }
                
                /* رسم خطي */
                .line-chart { height: 200px; margin-bottom: 20px; }
                .line-svg { width: 100%; height: 100%; }
                .chart-info { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; }
                .info-item { display: flex; justify-content: space-between; padding: 10px; background: #f8f9fa; border-radius: 8px; }
                .info-label { font-weight: 600; color: #666; }
                .info-value { font-weight: 700; color: #333; }
                
                /* إحصائيات المخزون */
                .inventory-stats { display: grid; gap: 15px; }
                .inventory-item { display: flex; align-items: center; gap: 15px; padding: 15px; background: #f8f9fa; border-radius: 10px; }
                .inventory-icon { font-size: 1.5rem; }
                .inventory-content { flex: 1; }
                .inventory-label { font-weight: 600; color: #333; margin-bottom: 5px; }
                .inventory-value { font-size: 0.9rem; color: #666; margin-bottom: 8px; }
                .inventory-bar { height: 6px; background: #e2e8f0; border-radius: 3px; overflow: hidden; }
                .inventory-progress { height: 100%; border-radius: 3px; transition: width 0.3s ease; }
            `;
            document.head.appendChild(style);
        }
    }
}

// إنشاء مثيل من مدير الرسوم البيانية
const chartsManager = new ChartsManager();
