const fs = require('fs');
const path = require('path');

class DataStorage {
    constructor() {
        this.dataPath = path.join(process.cwd(), 'data');
        this.ensureDataDirectory();
        this.initializeDataFiles();
    }

    // التأكد من وجود مجلد البيانات
    ensureDataDirectory() {
        if (!fs.existsSync(this.dataPath)) {
            fs.mkdirSync(this.dataPath, { recursive: true });
        }
    }

    // تهيئة ملفات البيانات الأساسية
    initializeDataFiles() {
        const defaultFiles = {
            'users.json': [],
            'products.json': [
                {
                    id: '1',
                    name: 'كنبة مودرن 3 مقاعد',
                    category: 'كنب',
                    price: 2500,
                    cost: 1800,
                    stock: 10,
                    description: 'كنبة مودرن عالية الجودة بتصميم أنيق',
                    createdAt: new Date().toISOString()
                },
                {
                    id: '2',
                    name: 'طاولة طعام خشبية',
                    category: 'طاولات',
                    price: 1200,
                    cost: 800,
                    stock: 5,
                    description: 'طاولة طعام من الخشب الطبيعي لـ 6 أشخاص',
                    createdAt: new Date().toISOString()
                }
            ],
            'customers.json': [
                {
                    id: '1',
                    name: 'أحمد محمد',
                    phone: '01234567890',
                    address: 'القاهرة، مصر الجديدة',
                    email: '<EMAIL>',
                    createdAt: new Date().toISOString()
                }
            ],
            'invoices.json': [],
            'settings.json': {
                storeName: 'مصطفي كشاف للمفروشات',
                storeAddress: 'القاهرة، مصر',
                storePhone: '02-12345678',
                storeEmail: '<EMAIL>',
                taxRate: 14,
                currency: 'جنيه مصري',
                invoicePrefix: 'INV-',
                nextInvoiceNumber: 1001
            }
        };

        Object.keys(defaultFiles).forEach(filename => {
            const filePath = path.join(this.dataPath, filename);
            if (!fs.existsSync(filePath)) {
                this.writeFile(filename, defaultFiles[filename]);
            }
        });
    }

    // قراءة ملف JSON
    readFile(filename) {
        try {
            const filePath = path.join(this.dataPath, filename);
            if (fs.existsSync(filePath)) {
                const data = fs.readFileSync(filePath, 'utf8');
                return JSON.parse(data);
            }
            return null;
        } catch (error) {
            console.error(`خطأ في قراءة الملف ${filename}:`, error);
            return null;
        }
    }

    // كتابة ملف JSON
    writeFile(filename, data) {
        try {
            const filePath = path.join(this.dataPath, filename);
            fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');
            return true;
        } catch (error) {
            console.error(`خطأ في كتابة الملف ${filename}:`, error);
            return false;
        }
    }

    // إضافة عنصر جديد
    addItem(filename, item) {
        const data = this.readFile(filename) || [];
        if (Array.isArray(data)) {
            data.push(item);
            return this.writeFile(filename, data);
        }
        return false;
    }

    // تحديث عنصر
    updateItem(filename, id, updatedItem) {
        const data = this.readFile(filename);
        if (Array.isArray(data)) {
            const index = data.findIndex(item => item.id === id);
            if (index !== -1) {
                data[index] = { ...data[index], ...updatedItem };
                return this.writeFile(filename, data);
            }
        }
        return false;
    }

    // حذف عنصر
    deleteItem(filename, id) {
        const data = this.readFile(filename);
        if (Array.isArray(data)) {
            const filteredData = data.filter(item => item.id !== id);
            return this.writeFile(filename, filteredData);
        }
        return false;
    }

    // البحث في البيانات
    searchItems(filename, searchTerm, fields = []) {
        const data = this.readFile(filename);
        if (!Array.isArray(data) || !searchTerm) {
            return data || [];
        }

        return data.filter(item => {
            if (fields.length === 0) {
                // البحث في جميع الحقول
                return Object.values(item).some(value => 
                    String(value).toLowerCase().includes(searchTerm.toLowerCase())
                );
            } else {
                // البحث في حقول محددة
                return fields.some(field => 
                    item[field] && String(item[field]).toLowerCase().includes(searchTerm.toLowerCase())
                );
            }
        });
    }

    // الحصول على عنصر بالمعرف
    getItemById(filename, id) {
        const data = this.readFile(filename);
        if (Array.isArray(data)) {
            return data.find(item => item.id === id);
        }
        return null;
    }

    // إنشاء نسخة احتياطية
    createBackup() {
        try {
            const backupPath = path.join(this.dataPath, 'backups');
            if (!fs.existsSync(backupPath)) {
                fs.mkdirSync(backupPath, { recursive: true });
            }

            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const backupFolder = path.join(backupPath, `backup-${timestamp}`);
            fs.mkdirSync(backupFolder);

            const files = fs.readdirSync(this.dataPath);
            files.forEach(file => {
                if (file.endsWith('.json') && file !== 'backups') {
                    const sourcePath = path.join(this.dataPath, file);
                    const destPath = path.join(backupFolder, file);
                    fs.copyFileSync(sourcePath, destPath);
                }
            });

            return backupFolder;
        } catch (error) {
            console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
            return null;
        }
    }

    // الحصول على إحصائيات
    getStatistics() {
        const products = this.readFile('products.json') || [];
        const customers = this.readFile('customers.json') || [];
        const invoices = this.readFile('invoices.json') || [];

        const totalProducts = products.length;
        const totalCustomers = customers.length;
        const totalInvoices = invoices.length;
        
        const totalSales = invoices.reduce((sum, invoice) => sum + (invoice.total || 0), 0);
        const totalProfit = invoices.reduce((sum, invoice) => sum + (invoice.profit || 0), 0);

        const lowStockProducts = products.filter(product => product.stock <= 5);

        return {
            totalProducts,
            totalCustomers,
            totalInvoices,
            totalSales,
            totalProfit,
            lowStockProducts: lowStockProducts.length,
            lowStockItems: lowStockProducts
        };
    }
}

// إنشاء مثيل واحد من كلاس التخزين
const storage = new DataStorage();

// تصدير الكلاس والمثيل
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { DataStorage, storage };
} else {
    window.storage = storage;
}
