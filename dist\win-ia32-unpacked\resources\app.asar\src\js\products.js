// إدارة المنتجات
class ProductManager {
    constructor() {
        this.products = [];
        this.loadProducts();
    }

    loadProducts() {
        this.products = storage.readFile('products.json') || [];
    }

    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    }

    addProduct(productData) {
        const product = {
            id: this.generateId(),
            name: productData.name,
            category: productData.category,
            price: parseFloat(productData.price),
            cost: parseFloat(productData.cost || 0),
            stock: parseInt(productData.stock || 0),
            description: productData.description || '',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        if (storage.addItem('products.json', product)) {
            this.loadProducts();
            return { success: true, product };
        }
        return { success: false, message: 'فشل في إضافة المنتج' };
    }

    updateProduct(id, productData) {
        const updatedData = {
            name: productData.name,
            category: productData.category,
            price: parseFloat(productData.price),
            cost: parseFloat(productData.cost || 0),
            stock: parseInt(productData.stock || 0),
            description: productData.description || '',
            updatedAt: new Date().toISOString()
        };

        if (storage.updateItem('products.json', id, updatedData)) {
            this.loadProducts();
            return { success: true };
        }
        return { success: false, message: 'فشل في تحديث المنتج' };
    }

    deleteProduct(id) {
        if (storage.deleteItem('products.json', id)) {
            this.loadProducts();
            return { success: true };
        }
        return { success: false, message: 'فشل في حذف المنتج' };
    }

    searchProducts(searchTerm) {
        return storage.searchItems('products.json', searchTerm, ['name', 'category', 'description']);
    }

    getProductById(id) {
        return storage.getItemById('products.json', id);
    }

    updateStock(id, quantity) {
        const product = this.getProductById(id);
        if (product) {
            const newStock = Math.max(0, product.stock - quantity); // تأكد من عدم النزول تحت الصفر
            const result = this.updateProduct(id, { ...product, stock: newStock });

            // تسجيل حركة المخزون
            console.log(`تم خصم ${quantity} من المنتج ${product.name}. المخزون الجديد: ${newStock}`);

            return result;
        }
        return { success: false, message: 'المنتج غير موجود' };
    }
}

const productManager = new ProductManager();

// تحميل صفحة المنتجات
function loadProducts() {
    const products = productManager.products;
    const productsHTML = `
        <div class="products-container">
            <div class="search-bar">
                <div class="search-input-group">
                    <i class="fas fa-search"></i>
                    <input type="text" id="productSearch" placeholder="البحث في المنتجات..." onkeyup="searchProducts()">
                </div>
            </div>

            <div class="products-grid" id="productsGrid">
                ${products.map(product => `
                    <div class="product-card" data-id="${product.id}">
                        <div class="product-header">
                            <h3>${product.name}</h3>
                            <div class="product-actions">
                                <button class="btn-icon edit" onclick="editProduct('${product.id}')" title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn-icon delete" onclick="deleteProduct('${product.id}')" title="حذف">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="product-info">
                            <div class="info-row">
                                <span class="label">الفئة:</span>
                                <span class="value">${product.category}</span>
                            </div>
                            <div class="info-row">
                                <span class="label">السعر:</span>
                                <span class="value price">${product.price.toLocaleString()} جنيه</span>
                            </div>
                            <div class="info-row">
                                <span class="label">المخزون:</span>
                                <span class="value stock ${product.stock < 5 ? 'low' : ''}">${product.stock} قطعة</span>
                            </div>
                        </div>
                        
                        ${product.description ? `
                            <div class="product-description">
                                <p>${product.description}</p>
                            </div>
                        ` : ''}
                    </div>
                `).join('')}
            </div>
        </div>

        <!-- نافذة إضافة/تعديل المنتج -->
        <div id="productModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2 id="modalTitle">إضافة منتج جديد</h2>
                    <button class="close-btn" onclick="closeProductModal()">&times;</button>
                </div>
                
                <form id="productForm" onsubmit="saveProduct(event)">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="productName">اسم المنتج *</label>
                            <input type="text" id="productName" required>
                        </div>
                        <div class="form-group">
                            <label for="productCategory">الفئة *</label>
                            <select id="productCategory" required>
                                <option value="">اختر الفئة</option>
                                <option value="كنب">كنب</option>
                                <option value="طاولات">طاولات</option>
                                <option value="كراسي">كراسي</option>
                                <option value="خزائن">خزائن</option>
                                <option value="أسرة">أسرة</option>
                                <option value="ديكورات">ديكورات</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="productPrice">سعر البيع *</label>
                            <input type="number" id="productPrice" step="0.01" min="0" required>
                        </div>
                        <div class="form-group">
                            <label for="productCost">سعر التكلفة</label>
                            <input type="number" id="productCost" step="0.01" min="0">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="productStock">الكمية في المخزون</label>
                        <input type="number" id="productStock" min="0" value="0">
                    </div>
                    
                    <div class="form-group">
                        <label for="productDescription">الوصف</label>
                        <textarea id="productDescription" rows="3"></textarea>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="closeProductModal()">
                            إلغاء
                        </button>
                    </div>
                </form>
            </div>
        </div>
    `;

    document.getElementById('productsContent').innerHTML = productsHTML;
    addProductStyles();
}

// البحث في المنتجات
function searchProducts() {
    const searchTerm = document.getElementById('productSearch').value;
    const products = searchTerm ? productManager.searchProducts(searchTerm) : productManager.products;
    
    const productsGrid = document.getElementById('productsGrid');
    productsGrid.innerHTML = products.map(product => `
        <div class="product-card" data-id="${product.id}">
            <div class="product-header">
                <h3>${product.name}</h3>
                <div class="product-actions">
                    <button class="btn-icon edit" onclick="editProduct('${product.id}')" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-icon delete" onclick="deleteProduct('${product.id}')" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
            
            <div class="product-info">
                <div class="info-row">
                    <span class="label">الفئة:</span>
                    <span class="value">${product.category}</span>
                </div>
                <div class="info-row">
                    <span class="label">السعر:</span>
                    <span class="value price">${product.price.toLocaleString()} جنيه</span>
                </div>
                <div class="info-row">
                    <span class="label">المخزون:</span>
                    <span class="value stock ${product.stock < 5 ? 'low' : ''}">${product.stock} قطعة</span>
                </div>
            </div>
            
            ${product.description ? `
                <div class="product-description">
                    <p>${product.description}</p>
                </div>
            ` : ''}
        </div>
    `).join('');
}

// إظهار نافذة إضافة منتج
function showAddProductModal() {
    document.getElementById('modalTitle').textContent = 'إضافة منتج جديد';
    document.getElementById('productForm').reset();
    document.getElementById('productForm').removeAttribute('data-edit-id');
    document.getElementById('productModal').style.display = 'flex';
}

// تعديل منتج
function editProduct(id) {
    const product = productManager.getProductById(id);
    if (product) {
        document.getElementById('modalTitle').textContent = 'تعديل المنتج';
        document.getElementById('productName').value = product.name;
        document.getElementById('productCategory').value = product.category;
        document.getElementById('productPrice').value = product.price;
        document.getElementById('productCost').value = product.cost;
        document.getElementById('productStock').value = product.stock;
        document.getElementById('productDescription').value = product.description;
        document.getElementById('productForm').setAttribute('data-edit-id', id);
        document.getElementById('productModal').style.display = 'flex';
    }
}

// حفظ المنتج
function saveProduct(event) {
    event.preventDefault();
    
    const formData = {
        name: document.getElementById('productName').value,
        category: document.getElementById('productCategory').value,
        price: document.getElementById('productPrice').value,
        cost: document.getElementById('productCost').value,
        stock: document.getElementById('productStock').value,
        description: document.getElementById('productDescription').value
    };

    const editId = document.getElementById('productForm').getAttribute('data-edit-id');
    let result;

    if (editId) {
        result = productManager.updateProduct(editId, formData);
    } else {
        result = productManager.addProduct(formData);
    }

    if (result.success) {
        closeProductModal();
        loadProducts();
        alert(editId ? 'تم تحديث المنتج بنجاح' : 'تم إضافة المنتج بنجاح');
    } else {
        alert(result.message);
    }
}

// حذف منتج
function deleteProduct(id) {
    if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
        const result = productManager.deleteProduct(id);
        if (result.success) {
            loadProducts();
            alert('تم حذف المنتج بنجاح');
        } else {
            alert(result.message);
        }
    }
}

// إغلاق نافذة المنتج
function closeProductModal() {
    document.getElementById('productModal').style.display = 'none';
}

// إضافة أنماط المنتجات
function addProductStyles() {
    if (!document.getElementById('productStyles')) {
        const style = document.createElement('style');
        style.id = 'productStyles';
        style.textContent = `
            .products-container { padding: 0; }
            .search-bar { margin-bottom: 25px; }
            .search-input-group { position: relative; max-width: 400px; }
            .search-input-group i { position: absolute; right: 15px; top: 50%; transform: translateY(-50%); color: #666; }
            .search-input-group input { width: 100%; padding: 12px 45px 12px 15px; border: 2px solid #e1e5e9; border-radius: 10px; font-size: 1rem; }
            .products-grid { display: grid; grid-template-columns: repeat(auto-fill, minmax(350px, 1fr)); gap: 20px; }
            .product-card { background: white; border-radius: 15px; padding: 20px; box-shadow: 0 5px 15px rgba(0,0,0,0.08); transition: transform 0.3s ease; }
            .product-card:hover { transform: translateY(-5px); }
            .product-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
            .product-header h3 { color: #333; font-size: 1.2rem; margin: 0; }
            .product-actions { display: flex; gap: 8px; }
            .btn-icon { width: 35px; height: 35px; border: none; border-radius: 8px; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease; }
            .btn-icon.edit { background: #e3f2fd; color: #1976d2; }
            .btn-icon.delete { background: #ffebee; color: #d32f2f; }
            .btn-icon:hover { transform: scale(1.1); }
            .product-info { margin-bottom: 15px; }
            .info-row { display: flex; justify-content: space-between; margin-bottom: 8px; }
            .info-row .label { color: #666; font-weight: 500; }
            .info-row .value { color: #333; font-weight: 600; }
            .info-row .price { color: #2e7d32; }
            .info-row .stock.low { color: #d32f2f; }
            .product-description { padding-top: 15px; border-top: 1px solid #eee; }
            .product-description p { color: #666; margin: 0; font-size: 0.9rem; }
            .modal { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; align-items: center; justify-content: center; }
            .modal-content { background: white; border-radius: 15px; width: 90%; max-width: 600px; max-height: 90vh; overflow-y: auto; }
            .modal-header { padding: 20px 25px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center; }
            .modal-header h2 { margin: 0; color: #333; }
            .close-btn { background: none; border: none; font-size: 1.5rem; cursor: pointer; color: #666; }
            .modal form { padding: 25px; }
            .form-row { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
            .form-group { margin-bottom: 20px; }
            .form-group label { display: block; margin-bottom: 8px; font-weight: 600; color: #333; }
            .form-group input, .form-group select, .form-group textarea { width: 100%; padding: 12px; border: 2px solid #e1e5e9; border-radius: 8px; font-size: 1rem; }
            .form-group input:focus, .form-group select:focus, .form-group textarea:focus { outline: none; border-color: #667eea; }
            .form-actions { display: flex; gap: 15px; justify-content: flex-end; margin-top: 25px; }
            .btn { padding: 12px 24px; border: none; border-radius: 8px; cursor: pointer; font-weight: 600; display: flex; align-items: center; gap: 8px; }
            .btn-primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
            .btn-secondary { background: #f8f9fa; color: #666; border: 1px solid #dee2e6; }
        `;
        document.head.appendChild(style);
    }
}
