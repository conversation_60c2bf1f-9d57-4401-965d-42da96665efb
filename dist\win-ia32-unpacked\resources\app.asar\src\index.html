<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مصطفي كشاف للمفروشات - نظام المبيعات</title>
    <link rel="stylesheet" href="css/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- شاشة تسجيل الدخول -->
    <div id="loginScreen" class="login-screen">
        <div class="login-container">
            <div class="login-header">
                <div class="logo-container">
                    <i class="fas fa-couch logo-icon"></i>
                    <h1>مصطفي كشاف للمفروشات</h1>
                    <p>نظام إدارة المبيعات</p>
                </div>
            </div>
            
            <div class="login-form">
                <div class="form-group">
                    <label for="username">اسم المستخدم</label>
                    <div class="input-group">
                        <i class="fas fa-user"></i>
                        <input type="text" id="username" placeholder="أدخل اسم المستخدم">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <div class="input-group">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="password" placeholder="أدخل كلمة المرور">
                        <i class="fas fa-eye toggle-password" onclick="togglePassword()"></i>
                    </div>
                </div>
                
                <button type="button" class="login-btn" onclick="login()">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                </button>
                
                <div class="login-options">
                    <a href="#" onclick="showRegisterForm()">إنشاء حساب جديد</a>
                </div>
            </div>
            
            <div class="error-message" id="errorMessage"></div>
        </div>
    </div>

    <!-- شاشة التسجيل -->
    <div id="registerScreen" class="login-screen" style="display: none;">
        <div class="login-container">
            <div class="login-header">
                <div class="logo-container">
                    <i class="fas fa-couch logo-icon"></i>
                    <h1>إنشاء حساب جديد</h1>
                    <p>مصطفي كشاف للمفروشات</p>
                </div>
            </div>
            
            <div class="login-form">
                <div class="form-group">
                    <label for="newUsername">اسم المستخدم</label>
                    <div class="input-group">
                        <i class="fas fa-user"></i>
                        <input type="text" id="newUsername" placeholder="أدخل اسم المستخدم">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="fullName">الاسم الكامل</label>
                    <div class="input-group">
                        <i class="fas fa-id-card"></i>
                        <input type="text" id="fullName" placeholder="أدخل الاسم الكامل">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="newPassword">كلمة المرور</label>
                    <div class="input-group">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="newPassword" placeholder="أدخل كلمة المرور">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="confirmPassword">تأكيد كلمة المرور</label>
                    <div class="input-group">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="confirmPassword" placeholder="أعد إدخال كلمة المرور">
                    </div>
                </div>
                
                <button type="button" class="login-btn" onclick="register()">
                    <i class="fas fa-user-plus"></i>
                    إنشاء الحساب
                </button>
                
                <div class="login-options">
                    <a href="#" onclick="showLoginForm()">العودة لتسجيل الدخول</a>
                </div>
            </div>
            
            <div class="error-message" id="registerErrorMessage"></div>
        </div>
    </div>

    <!-- الواجهة الرئيسية -->
    <div id="mainApp" class="main-app" style="display: none;">
        <!-- شريط التنقل العلوي -->
        <nav class="navbar">
            <div class="nav-brand">
                <i class="fas fa-couch"></i>
                <span>مصطفي كشاف للمفروشات</span>
            </div>

            <div class="nav-user">
                <span id="currentUser">مرحباً، </span>
                <button class="logout-btn" onclick="logout()">
                    <i class="fas fa-sign-out-alt"></i>
                    تسجيل الخروج
                </button>
            </div>
        </nav>

        <!-- الشريط الجانبي -->
        <aside class="sidebar">
            <ul class="sidebar-menu">
                <li class="menu-item active" data-page="dashboard">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>لوحة التحكم</span>
                </li>
                <li class="menu-item" data-page="products">
                    <i class="fas fa-box"></i>
                    <span>المنتجات</span>
                </li>
                <li class="menu-item" data-page="customers">
                    <i class="fas fa-users"></i>
                    <span>العملاء</span>
                </li>
                <li class="menu-item" data-page="invoices">
                    <i class="fas fa-file-invoice"></i>
                    <span>الفواتير</span>
                </li>
                <li class="menu-item" data-page="reports">
                    <i class="fas fa-chart-bar"></i>
                    <span>التقارير</span>
                </li>
                <li class="menu-item" data-page="activity">
                    <i class="fas fa-history"></i>
                    <span>سجل الأنشطة</span>
                </li>
                <li class="menu-item" data-page="settings">
                    <i class="fas fa-cog"></i>
                    <span>الإعدادات</span>
                </li>
            </ul>
        </aside>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <div id="pageContent">
                <!-- سيتم تحميل المحتوى هنا -->
            </div>
        </main>
    </div>

    <!-- تحميل ملفات JavaScript -->
    <script src="js/storage.js"></script>
    <script src="js/activity-log.js"></script>
    <script src="js/backup.js"></script>
    <script src="js/utils.js"></script>
    <script src="js/search.js"></script>
    <script src="js/charts.js"></script>
    <script src="js/shortcuts.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/app.js"></script>
    <script src="js/products.js"></script>
    <script src="js/customers.js"></script>
    <script src="js/invoice.js"></script>
    <script src="js/print.js"></script>
</body>
</html>
