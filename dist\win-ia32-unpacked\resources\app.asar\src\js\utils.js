// دوال مساعدة للتطبيق
class AppUtils {
    constructor() {
        this.init();
    }

    init() {
        // إعداد دوال مساعدة عامة
    }

    // تصدير البيانات
    exportData(data, filename, format = 'json') {
        try {
            let content;
            let mimeType;
            let fileExtension;

            switch (format.toLowerCase()) {
                case 'csv':
                    content = this.convertToCSV(data);
                    mimeType = 'text/csv';
                    fileExtension = 'csv';
                    break;
                case 'json':
                default:
                    content = JSON.stringify(data, null, 2);
                    mimeType = 'application/json';
                    fileExtension = 'json';
                    break;
            }

            const blob = new Blob([content], { type: mimeType });
            const url = window.URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `${filename}.${fileExtension}`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            return true;
        } catch (error) {
            console.error('خطأ في تصدير البيانات:', error);
            return false;
        }
    }

    // تحويل البيانات إلى CSV
    convertToCSV(data) {
        if (!Array.isArray(data) || data.length === 0) {
            return '';
        }

        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => 
                headers.map(header => {
                    const value = row[header];
                    // تنظيف القيم وإضافة علامات اقتباس إذا لزم الأمر
                    if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
                        return `"${value.replace(/"/g, '""')}"`;
                    }
                    return value;
                }).join(',')
            )
        ].join('\n');

        return csvContent;
    }

    // تنسيق التاريخ
    formatDate(date, format = 'full') {
        const d = new Date(date);
        
        switch (format) {
            case 'date':
                return d.toLocaleDateString('ar-EG');
            case 'time':
                return d.toLocaleTimeString('ar-EG');
            case 'datetime':
                return `${d.toLocaleDateString('ar-EG')} ${d.toLocaleTimeString('ar-EG')}`;
            case 'full':
            default:
                return d.toLocaleString('ar-EG');
        }
    }

    // تنسيق الأرقام
    formatNumber(number, decimals = 2) {
        return Number(number).toLocaleString('ar-EG', {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        });
    }

    // تنسيق العملة
    formatCurrency(amount, currency = 'جنيه') {
        return `${this.formatNumber(amount)} ${currency}`;
    }

    // إنشاء معرف فريد
    generateUniqueId() {
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    }

    // التحقق من صحة البريد الإلكتروني
    validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // التحقق من صحة رقم الهاتف المصري
    validateEgyptianPhone(phone) {
        const phoneRegex = /^(01)[0-9]{9}$/;
        return phoneRegex.test(phone.replace(/\s+/g, ''));
    }

    // تنظيف النص
    sanitizeText(text) {
        return text.replace(/[<>]/g, '').trim();
    }

    // حساب النسبة المئوية
    calculatePercentage(value, total) {
        if (total === 0) return 0;
        return ((value / total) * 100).toFixed(2);
    }

    // تحويل الحجم بالبايت إلى وحدة مقروءة
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // إظهار رسالة تأكيد
    showConfirmDialog(message, onConfirm, onCancel = null) {
        const result = confirm(message);
        if (result && onConfirm) {
            onConfirm();
        } else if (!result && onCancel) {
            onCancel();
        }
        return result;
    }

    // إظهار رسالة نجاح
    showSuccessMessage(message, duration = 3000) {
        this.showToast(message, 'success', duration);
    }

    // إظهار رسالة خطأ
    showErrorMessage(message, duration = 5000) {
        this.showToast(message, 'error', duration);
    }

    // إظهار رسالة معلومات
    showInfoMessage(message, duration = 3000) {
        this.showToast(message, 'info', duration);
    }

    // إظهار Toast notification
    showToast(message, type = 'info', duration = 3000) {
        // إنشاء عنصر Toast
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="fas ${this.getToastIcon(type)}"></i>
                <span>${message}</span>
            </div>
        `;

        // إضافة الأنماط إذا لم تكن موجودة
        this.addToastStyles();

        // إضافة Toast إلى الصفحة
        document.body.appendChild(toast);

        // إظهار Toast
        setTimeout(() => toast.classList.add('show'), 100);

        // إخفاء وحذف Toast
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, duration);
    }

    getToastIcon(type) {
        switch (type) {
            case 'success': return 'fa-check-circle';
            case 'error': return 'fa-times-circle';
            case 'warning': return 'fa-exclamation-triangle';
            default: return 'fa-info-circle';
        }
    }

    addToastStyles() {
        if (!document.getElementById('toastStyles')) {
            const style = document.createElement('style');
            style.id = 'toastStyles';
            style.textContent = `
                .toast {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: white;
                    border-radius: 10px;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
                    padding: 15px 20px;
                    z-index: 10000;
                    transform: translateX(400px);
                    transition: transform 0.3s ease;
                    border-left: 4px solid #3182ce;
                }
                .toast.show { transform: translateX(0); }
                .toast.toast-success { border-left-color: #38a169; }
                .toast.toast-error { border-left-color: #e53e3e; }
                .toast.toast-warning { border-left-color: #d69e2e; }
                .toast-content { display: flex; align-items: center; gap: 10px; }
                .toast-content i { font-size: 1.2rem; }
                .toast.toast-success i { color: #38a169; }
                .toast.toast-error i { color: #e53e3e; }
                .toast.toast-warning i { color: #d69e2e; }
                .toast.toast-info i { color: #3182ce; }
            `;
            document.head.appendChild(style);
        }
    }

    // طباعة عنصر HTML
    printElement(elementId) {
        const element = document.getElementById(elementId);
        if (!element) return false;

        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>طباعة</title>
                    <style>
                        body { font-family: 'Cairo', Arial, sans-serif; }
                        @media print { body { margin: 0; } }
                    </style>
                </head>
                <body>
                    ${element.innerHTML}
                </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.print();
        return true;
    }
}

// إنشاء مثيل من الأدوات المساعدة
const appUtils = new AppUtils();

// دوال مساعدة للاستخدام السريع
function exportActivityLogs() {
    if (typeof activityLogger !== 'undefined') {
        const logs = activityLogger.getLogs(1000);
        const success = appUtils.exportData(logs, `activity-logs-${new Date().toISOString().split('T')[0]}`, 'csv');
        if (success) {
            appUtils.showSuccessMessage('تم تصدير سجل الأنشطة بنجاح');
        } else {
            appUtils.showErrorMessage('فشل في تصدير سجل الأنشطة');
        }
    }
}

function filterActivityLogs() {
    const level = document.getElementById('levelFilter').value;
    const logs = typeof activityLogger !== 'undefined' ? activityLogger.getLogs(50, level) : [];

    const logEntries = document.getElementById('logEntries');
    if (logEntries) {
        logEntries.innerHTML = logs.length > 0 ? logs.map(log => `
            <div class="log-entry ${log.level}">
                <div class="log-icon">
                    <i class="fas ${app.getLogIcon(log.level)}"></i>
                </div>
                <div class="log-content">
                    <div class="log-action">${log.action}</div>
                    <div class="log-details">${JSON.stringify(log.details)}</div>
                    <div class="log-meta">
                        <span class="log-user">${log.user}</span>
                        <span class="log-time">${log.date} ${log.time}</span>
                    </div>
                </div>
            </div>
        `).join('') : '<div class="no-logs">لا توجد أنشطة مسجلة</div>';
    }
}

// دوال التقارير
function exportReports() {
    const stats = storage.getStatistics();
    const reportData = {
        generatedAt: new Date().toISOString(),
        generatedBy: authManager.getCurrentUser()?.fullName || 'غير محدد',
        summary: stats,
        products: storage.readFile('products.json') || [],
        customers: storage.readFile('customers.json') || [],
        invoices: storage.readFile('invoices.json') || []
    };

    const success = appUtils.exportData(reportData, `تقرير-شامل-${new Date().toISOString().split('T')[0]}`, 'json');
    if (success) {
        appUtils.showSuccessMessage('تم تصدير التقرير بنجاح');
    } else {
        appUtils.showErrorMessage('فشل في تصدير التقرير');
    }
}

function refreshReports() {
    app.loadPage('reports');
    appUtils.showSuccessMessage('تم تحديث التقارير');
}
