const { app, BrowserWindow, Menu, ipcMain, dialog } = require('electron');
const path = require('path');
const fs = require('fs');

// تعطيل قائمة التطبيق الافتراضية
Menu.setApplicationMenu(null);

let mainWindow;

function createWindow() {
  // إنشاء نافذة المتصفح
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 700,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
      enableRemoteModule: true
    },
    icon: path.join(__dirname, 'assets', 'icon.png'),
    show: false,
    titleBarStyle: 'default',
    frame: true
  });

  // تحميل ملف HTML
  mainWindow.loadFile('src/index.html');

  // إظهار النافذة عند الانتهاء من التحميل
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    mainWindow.maximize();
  });

  // فتح أدوات المطور في وضع التطوير
  if (process.argv.includes('--dev')) {
    mainWindow.webContents.openDevTools();
  }
}

// هذا الأسلوب سيتم استدعاؤه عندما ينتهي Electron من التهيئة
app.whenReady().then(createWindow);

// الخروج عندما يتم إغلاق جميع النوافذ
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// إنشاء مجلدات البيانات إذا لم تكن موجودة
function ensureDataDirectories() {
  const directories = ['data', 'assets', 'templates'];
  directories.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  });
}

// التعامل مع طلبات الطباعة
ipcMain.handle('print-invoice', async (event, htmlContent) => {
  try {
    const printWindow = new BrowserWindow({
      width: 800,
      height: 600,
      show: false,
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false
      }
    });

    await printWindow.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(htmlContent)}`);
    
    const result = await printWindow.webContents.print({
      silent: false,
      printBackground: true,
      margins: {
        marginType: 'minimum'
      }
    });

    printWindow.close();
    return result;
  } catch (error) {
    console.error('خطأ في الطباعة:', error);
    return false;
  }
});

// إنشاء المجلدات عند بدء التطبيق
app.whenReady().then(() => {
  ensureDataDirectories();
});
