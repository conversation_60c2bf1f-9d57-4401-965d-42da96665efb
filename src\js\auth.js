// إدارة المصادقة وتسجيل الدخول
class AuthManager {
    constructor() {
        this.currentUser = null;
        this.initializeDefaultAdmin();
    }

    // إنشاء حساب المدير الافتراضي
    initializeDefaultAdmin() {
        const users = storage.readFile('users.json') || [];
        if (users.length === 0) {
            const defaultAdmin = {
                id: this.generateId(),
                username: 'admin',
                password: this.hashPassword('123456'),
                fullName: 'مدير النظام',
                role: 'admin',
                isActive: true,
                createdAt: new Date().toISOString(),
                lastLogin: null
            };
            storage.addItem('users.json', defaultAdmin);
        }
    }

    // تشفير كلمة المرور (تشفير بسيط)
    hashPassword(password) {
        // في التطبيق الحقيقي، استخدم مكتبة تشفير قوية
        let hash = 0;
        for (let i = 0; i < password.length; i++) {
            const char = password.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // تحويل إلى 32bit integer
        }
        return Math.abs(hash).toString(16);
    }

    // إنشاء معرف فريد
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    }

    // تسجيل الدخول
    login(username, password) {
        try {
            const users = storage.readFile('users.json') || [];
            const hashedPassword = this.hashPassword(password);
            
            const user = users.find(u => 
                u.username === username && 
                u.password === hashedPassword && 
                u.isActive
            );

            if (user) {
                // تحديث وقت آخر تسجيل دخول
                user.lastLogin = new Date().toISOString();
                storage.updateItem('users.json', user.id, user);
                
                this.currentUser = {
                    id: user.id,
                    username: user.username,
                    fullName: user.fullName,
                    role: user.role
                };

                // حفظ جلسة المستخدم
                localStorage.setItem('currentUser', JSON.stringify(this.currentUser));
                
                return { success: true, user: this.currentUser };
            } else {
                return { success: false, message: 'اسم المستخدم أو كلمة المرور غير صحيحة' };
            }
        } catch (error) {
            console.error('خطأ في تسجيل الدخول:', error);
            return { success: false, message: 'حدث خطأ أثناء تسجيل الدخول' };
        }
    }

    // تسجيل حساب جديد
    register(userData) {
        try {
            const { username, password, confirmPassword, fullName } = userData;

            // التحقق من صحة البيانات
            if (!username || !password || !fullName) {
                return { success: false, message: 'جميع الحقول مطلوبة' };
            }

            if (password !== confirmPassword) {
                return { success: false, message: 'كلمة المرور غير متطابقة' };
            }

            if (password.length < 6) {
                return { success: false, message: 'كلمة المرور يجب أن تكون 6 أحرف على الأقل' };
            }

            const users = storage.readFile('users.json') || [];
            
            // التحقق من عدم وجود اسم المستخدم
            if (users.find(u => u.username === username)) {
                return { success: false, message: 'اسم المستخدم موجود بالفعل' };
            }

            // إنشاء المستخدم الجديد
            const newUser = {
                id: this.generateId(),
                username: username.trim(),
                password: this.hashPassword(password),
                fullName: fullName.trim(),
                role: 'user',
                isActive: true,
                createdAt: new Date().toISOString(),
                lastLogin: null
            };

            if (storage.addItem('users.json', newUser)) {
                return { success: true, message: 'تم إنشاء الحساب بنجاح' };
            } else {
                return { success: false, message: 'حدث خطأ أثناء إنشاء الحساب' };
            }
        } catch (error) {
            console.error('خطأ في التسجيل:', error);
            return { success: false, message: 'حدث خطأ أثناء إنشاء الحساب' };
        }
    }

    // تسجيل الخروج
    logout() {
        this.currentUser = null;
        localStorage.removeItem('currentUser');
        this.showLoginScreen();
    }

    // التحقق من الجلسة المحفوظة
    checkSavedSession() {
        try {
            const savedUser = localStorage.getItem('currentUser');
            if (savedUser) {
                this.currentUser = JSON.parse(savedUser);
                return true;
            }
            return false;
        } catch (error) {
            console.error('خطأ في التحقق من الجلسة:', error);
            return false;
        }
    }

    // الحصول على المستخدم الحالي
    getCurrentUser() {
        return this.currentUser;
    }

    // التحقق من صلاحيات المستخدم
    hasPermission(permission) {
        if (!this.currentUser) return false;
        
        // المدير له جميع الصلاحيات
        if (this.currentUser.role === 'admin') return true;
        
        // يمكن إضافة منطق صلاحيات أكثر تعقيداً هنا
        return true;
    }

    // إظهار شاشة تسجيل الدخول
    showLoginScreen() {
        document.getElementById('loginScreen').style.display = 'flex';
        document.getElementById('registerScreen').style.display = 'none';
        document.getElementById('mainApp').style.display = 'none';
        
        // مسح الحقول
        document.getElementById('username').value = '';
        document.getElementById('password').value = '';
        document.getElementById('errorMessage').style.display = 'none';
    }

    // إظهار شاشة التسجيل
    showRegisterScreen() {
        document.getElementById('loginScreen').style.display = 'none';
        document.getElementById('registerScreen').style.display = 'flex';
        document.getElementById('mainApp').style.display = 'none';
        
        // مسح الحقول
        document.getElementById('newUsername').value = '';
        document.getElementById('fullName').value = '';
        document.getElementById('newPassword').value = '';
        document.getElementById('confirmPassword').value = '';
        document.getElementById('registerErrorMessage').style.display = 'none';
    }

    // إظهار التطبيق الرئيسي
    showMainApp() {
        document.getElementById('loginScreen').style.display = 'none';
        document.getElementById('registerScreen').style.display = 'none';
        document.getElementById('mainApp').style.display = 'flex';
        
        // تحديث اسم المستخدم في الشريط العلوي
        if (this.currentUser) {
            document.getElementById('currentUser').textContent = `مرحباً، ${this.currentUser.fullName}`;
        }
    }

    // إظهار رسالة خطأ
    showError(elementId, message) {
        const errorElement = document.getElementById(elementId);
        errorElement.textContent = message;
        errorElement.style.display = 'block';
        
        // إخفاء الرسالة بعد 5 ثوان
        setTimeout(() => {
            errorElement.style.display = 'none';
        }, 5000);
    }
}

// إنشاء مثيل من مدير المصادقة
const authManager = new AuthManager();

// دوال تسجيل الدخول والتسجيل
function login() {
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value;

    if (!username || !password) {
        authManager.showError('errorMessage', 'يرجى إدخال اسم المستخدم وكلمة المرور');
        return;
    }

    const result = authManager.login(username, password);
    
    if (result.success) {
        authManager.showMainApp();
        // تحميل لوحة التحكم
        loadDashboard();
    } else {
        authManager.showError('errorMessage', result.message);
    }
}

function register() {
    const userData = {
        username: document.getElementById('newUsername').value.trim(),
        fullName: document.getElementById('fullName').value.trim(),
        password: document.getElementById('newPassword').value,
        confirmPassword: document.getElementById('confirmPassword').value
    };

    const result = authManager.register(userData);
    
    if (result.success) {
        alert('تم إنشاء الحساب بنجاح! يمكنك الآن تسجيل الدخول');
        showLoginForm();
    } else {
        authManager.showError('registerErrorMessage', result.message);
    }
}

function logout() {
    if (confirm('هل تريد تسجيل الخروج؟')) {
        authManager.logout();
    }
}

function showLoginForm() {
    authManager.showLoginScreen();
}

function showRegisterForm() {
    authManager.showRegisterScreen();
}

function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.querySelector('.toggle-password');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

// التحقق من Enter key في حقول تسجيل الدخول
document.addEventListener('DOMContentLoaded', function() {
    const loginInputs = ['username', 'password'];
    loginInputs.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    login();
                }
            });
        }
    });
});
