// نظام تسجيل العمليات والأنشطة
class ActivityLogger {
    constructor() {
        this.logFile = 'activity-log.json';
        this.maxLogEntries = 1000; // الحد الأقصى لعدد السجلات
        this.init();
    }

    init() {
        // إنشاء ملف السجل إذا لم يكن موجوداً
        const logs = storage.readFile(this.logFile);
        if (!logs) {
            storage.writeFile(this.logFile, []);
        }
    }

    log(action, details = {}, level = 'info') {
        try {
            const logEntry = {
                id: this.generateId(),
                timestamp: new Date().toISOString(),
                date: new Date().toLocaleDateString('ar-EG'),
                time: new Date().toLocaleTimeString('ar-EG'),
                user: authManager.getCurrentUser()?.fullName || 'غير محدد',
                userId: authManager.getCurrentUser()?.id || null,
                action: action,
                details: details,
                level: level, // info, warning, error, success
                ip: this.getLocalIP(),
                userAgent: navigator.userAgent
            };

            const logs = storage.readFile(this.logFile) || [];
            logs.unshift(logEntry); // إضافة في المقدمة

            // الاحتفاظ بآخر 1000 سجل فقط
            if (logs.length > this.maxLogEntries) {
                logs.splice(this.maxLogEntries);
            }

            storage.writeFile(this.logFile, logs);
            
            // طباعة في وحدة التحكم للتطوير
            console.log(`[${level.toUpperCase()}] ${action}:`, details);
            
            return logEntry;
        } catch (error) {
            console.error('خطأ في تسجيل النشاط:', error);
        }
    }

    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substring(2);
    }

    getLocalIP() {
        // محاولة الحصول على IP المحلي (تقريبي)
        return 'localhost';
    }

    // تسجيل أنشطة مختلفة
    logLogin(username) {
        return this.log('تسجيل دخول', { username }, 'success');
    }

    logLogout(username) {
        return this.log('تسجيل خروج', { username }, 'info');
    }

    logProductAdd(product) {
        return this.log('إضافة منتج', { 
            productId: product.id, 
            productName: product.name,
            price: product.price,
            stock: product.stock
        }, 'success');
    }

    logProductUpdate(productId, oldData, newData) {
        return this.log('تعديل منتج', { 
            productId, 
            oldData, 
            newData,
            changes: this.getChanges(oldData, newData)
        }, 'info');
    }

    logProductDelete(product) {
        return this.log('حذف منتج', { 
            productId: product.id, 
            productName: product.name 
        }, 'warning');
    }

    logCustomerAdd(customer) {
        return this.log('إضافة عميل', { 
            customerId: customer.id, 
            customerName: customer.name,
            phone: customer.phone
        }, 'success');
    }

    logCustomerUpdate(customerId, oldData, newData) {
        return this.log('تعديل عميل', { 
            customerId, 
            oldData, 
            newData,
            changes: this.getChanges(oldData, newData)
        }, 'info');
    }

    logCustomerDelete(customer) {
        return this.log('حذف عميل', { 
            customerId: customer.id, 
            customerName: customer.name 
        }, 'warning');
    }

    logInvoiceCreate(invoice) {
        return this.log('إنشاء فاتورة', { 
            invoiceId: invoice.id,
            invoiceNumber: invoice.invoiceNumber,
            customerName: invoice.customerName,
            total: invoice.total,
            itemsCount: invoice.items.length
        }, 'success');
    }

    logInvoiceUpdate(invoiceId, oldData, newData) {
        return this.log('تعديل فاتورة', { 
            invoiceId,
            invoiceNumber: newData.invoiceNumber,
            oldTotal: oldData.total,
            newTotal: newData.total,
            changes: this.getChanges(oldData, newData)
        }, 'info');
    }

    logInvoiceDelete(invoice) {
        return this.log('حذف فاتورة', { 
            invoiceId: invoice.id,
            invoiceNumber: invoice.invoiceNumber,
            total: invoice.total
        }, 'warning');
    }

    logInvoicePrint(invoice) {
        return this.log('طباعة فاتورة', { 
            invoiceId: invoice.id,
            invoiceNumber: invoice.invoiceNumber,
            customerName: invoice.customerName,
            total: invoice.total
        }, 'info');
    }

    logBackupCreate(type) {
        return this.log('إنشاء نسخة احتياطية', { type }, 'success');
    }

    logBackupRestore(backupPath) {
        return this.log('استعادة نسخة احتياطية', { backupPath }, 'warning');
    }

    logError(error, context = {}) {
        return this.log('خطأ في النظام', { 
            error: error.message,
            stack: error.stack,
            context
        }, 'error');
    }

    // مقارنة البيانات لمعرفة التغييرات
    getChanges(oldData, newData) {
        const changes = {};
        Object.keys(newData).forEach(key => {
            if (oldData[key] !== newData[key]) {
                changes[key] = {
                    from: oldData[key],
                    to: newData[key]
                };
            }
        });
        return changes;
    }

    // الحصول على السجلات
    getLogs(limit = 100, level = null, action = null) {
        let logs = storage.readFile(this.logFile) || [];
        
        // تصفية حسب المستوى
        if (level) {
            logs = logs.filter(log => log.level === level);
        }
        
        // تصفية حسب النشاط
        if (action) {
            logs = logs.filter(log => log.action.includes(action));
        }
        
        return logs.slice(0, limit);
    }

    // الحصول على إحصائيات السجلات
    getLogStats() {
        const logs = storage.readFile(this.logFile) || [];
        const today = new Date().toLocaleDateString('ar-EG');
        
        const stats = {
            total: logs.length,
            today: logs.filter(log => log.date === today).length,
            byLevel: {},
            byAction: {},
            byUser: {}
        };

        logs.forEach(log => {
            // إحصائيات المستوى
            stats.byLevel[log.level] = (stats.byLevel[log.level] || 0) + 1;
            
            // إحصائيات النشاط
            stats.byAction[log.action] = (stats.byAction[log.action] || 0) + 1;
            
            // إحصائيات المستخدم
            stats.byUser[log.user] = (stats.byUser[log.user] || 0) + 1;
        });

        return stats;
    }

    // تصدير السجلات
    exportLogs(format = 'json') {
        const logs = storage.readFile(this.logFile) || [];
        
        if (format === 'csv') {
            return this.convertToCSV(logs);
        }
        
        return JSON.stringify(logs, null, 2);
    }

    convertToCSV(logs) {
        if (logs.length === 0) return '';
        
        const headers = ['التاريخ', 'الوقت', 'المستخدم', 'النشاط', 'المستوى', 'التفاصيل'];
        const csvContent = [
            headers.join(','),
            ...logs.map(log => [
                log.date,
                log.time,
                log.user,
                log.action,
                log.level,
                JSON.stringify(log.details).replace(/,/g, ';')
            ].join(','))
        ].join('\n');
        
        return csvContent;
    }

    // مسح السجلات القديمة
    clearOldLogs(daysToKeep = 30) {
        const logs = storage.readFile(this.logFile) || [];
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);
        
        const filteredLogs = logs.filter(log => {
            const logDate = new Date(log.timestamp);
            return logDate >= cutoffDate;
        });
        
        storage.writeFile(this.logFile, filteredLogs);
        
        return {
            removed: logs.length - filteredLogs.length,
            remaining: filteredLogs.length
        };
    }
}

// إنشاء مثيل من مسجل الأنشطة
const activityLogger = new ActivityLogger();

// دوال مساعدة للاستخدام السهل
function logActivity(action, details, level = 'info') {
    return activityLogger.log(action, details, level);
}

function getActivityLogs(limit = 100) {
    return activityLogger.getLogs(limit);
}

function getActivityStats() {
    return activityLogger.getLogStats();
}
