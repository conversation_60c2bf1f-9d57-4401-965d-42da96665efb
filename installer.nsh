; إعدادات مخصصة لمثبت مصطفي كشاف للمفروشات
; Custom installer settings for Mustafa Kashaf Furniture

; إضافة رسائل مخصصة
!define MUI_WELCOMEPAGE_TITLE "مرحباً بك في برنامج مصطفي كشاف للمفروشات"
!define MUI_WELCOMEPAGE_TEXT "هذا المعالج سيقوم بتثبيت نظام إدارة المبيعات الاحترافي على جهازك.$\r$\n$\r$\nيُنصح بإغلاق جميع التطبيقات الأخرى قبل المتابعة.$\r$\n$\r$\nانقر التالي للمتابعة."

!define MUI_FINISHPAGE_TITLE "تم تثبيت البرنامج بنجاح"
!define MUI_FINISHPAGE_TEXT "تم تثبيت برنامج مصطفي كشاف للمفروشات بنجاح على جهازك.$\r$\n$\r$\nيمكنك الآن تشغيل البرنامج من سطح المكتب أو قائمة ابدأ."

; إضافة معلومات إضافية
!define MUI_FINISHPAGE_RUN_TEXT "تشغيل برنامج مصطفي كشاف للمفروشات الآن"
!define MUI_FINISHPAGE_SHOWREADME_TEXT "عرض دليل الاستخدام"

; رسائل إلغاء التثبيت
!define MUI_UNCONFIRMPAGE_TEXT_TOP "سيتم إلغاء تثبيت برنامج مصطفي كشاف للمفروشات من جهازك."
!define MUI_UNCONFIRMPAGE_TEXT_LOCATION "سيتم إلغاء التثبيت من المجلد التالي:"

; إعدادات إضافية
RequestExecutionLevel admin
ShowInstDetails show
ShowUnInstDetails show

; إنشاء مجلدات إضافية
Section "إنشاء مجلدات البيانات" SEC01
    SetOutPath "$INSTDIR\data"
    SetOutPath "$INSTDIR\data\backups"
    SetOutPath "$INSTDIR\logs"
SectionEnd

; إنشاء اختصارات إضافية
Section "إنشاء الاختصارات" SEC02
    ; اختصار سطح المكتب
    CreateShortCut "$DESKTOP\مصطفي كشاف للمفروشات.lnk" "$INSTDIR\${APP_EXECUTABLE_FILENAME}" "" "$INSTDIR\resources\app\assets\icon.ico"
    
    ; اختصار قائمة ابدأ
    CreateDirectory "$SMPROGRAMS\مصطفي كشاف للمفروشات"
    CreateShortCut "$SMPROGRAMS\مصطفي كشاف للمفروشات\مصطفي كشاف للمفروشات.lnk" "$INSTDIR\${APP_EXECUTABLE_FILENAME}"
    CreateShortCut "$SMPROGRAMS\مصطفي كشاف للمفروشات\دليل الاستخدام.lnk" "$INSTDIR\دليل_التثبيت_والاستخدام.txt"
    CreateShortCut "$SMPROGRAMS\مصطفي كشاف للمفروشات\إلغاء التثبيت.lnk" "$INSTDIR\Uninstall.exe"
SectionEnd

; تسجيل البرنامج في النظام
Section "تسجيل البرنامج" SEC03
    ; إضافة إلى قائمة البرامج المثبتة
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_GUID}" "DisplayName" "مصطفي كشاف للمفروشات"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_GUID}" "DisplayVersion" "${VERSION}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_GUID}" "Publisher" "Mustafa Kashaf Furniture Store"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_GUID}" "DisplayIcon" "$INSTDIR\resources\app\assets\icon.ico"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_GUID}" "UninstallString" "$INSTDIR\Uninstall.exe"
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_GUID}" "NoModify" 1
    WriteRegDWORD HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_GUID}" "NoRepair" 1
SectionEnd

; دالة تشغيل بعد التثبيت
Function .onInstSuccess
    ; عرض رسالة نجاح
    MessageBox MB_ICONINFORMATION "تم تثبيت برنامج مصطفي كشاف للمفروشات بنجاح!$\r$\n$\r$\nبيانات تسجيل الدخول الافتراضية:$\r$\nاسم المستخدم: admin$\r$\nكلمة المرور: 123456"
FunctionEnd

; دالة إلغاء التثبيت
Section "Uninstall"
    ; حذف الملفات
    RMDir /r "$INSTDIR"
    
    ; حذف الاختصارات
    Delete "$DESKTOP\مصطفي كشاف للمفروشات.lnk"
    RMDir /r "$SMPROGRAMS\مصطفي كشاف للمفروشات"
    
    ; حذف مفاتيح التسجيل
    DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${APP_GUID}"
SectionEnd
